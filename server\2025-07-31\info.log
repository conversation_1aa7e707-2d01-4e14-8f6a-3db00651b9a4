[EzC2]2025-07-31 11:46:59.618	info	组件初始化成功	{"component": "zap", "duration": 0}
[EzC2]2025-07-31 11:46:59.619	info	组件初始化成功	{"component": "viper", "duration": 0.0015968}
[EzC2]2025-07-31 11:46:59.640	info	客户端事件管理器初始化完成
[EzC2]2025-07-31 11:46:59.640	info	智能缓存管理器创建成功	{"maxSize": 10000, "maxMemory": 104857600}
[EzC2]2025-07-31 11:46:59.641	info	全局智能缓存管理器初始化完成
[EzC2]2025-07-31 11:46:59.640	info	SSE管理器初始化完成
[EzC2]2025-07-31 11:46:59.640	info	未配置磁盘列表，将自动检测系统盘符
[EzC2]2025-07-31 11:46:59.640	info	工作池创建成功	{"name": "数据库操作池", "minWorkers": 2, "maxWorkers": 10, "queueSize": 500}
[EzC2]2025-07-31 11:46:59.640	info	组件初始化成功	{"component": "event_manager", "duration": 0.0005198}
[EzC2]2025-07-31 11:46:59.641	info	组件初始化成功	{"component": "smart_cache", "duration": 0.0010402}
[EzC2]2025-07-31 11:46:59.641	info	连接sqlite成功，已配置连接池参数	{"maxIdleConns": 10, "maxOpenConns": 100}
[EzC2]2025-07-31 11:46:59.641	info	组件初始化成功	{"component": "sse_manager", "duration": 0.0015576}
[EzC2]2025-07-31 11:46:59.642	info	自动检测到系统盘符: [C:\ E:\]
[EzC2]2025-07-31 11:46:59.642	info	工作池创建成功	{"poolType": "database", "name": "数据库操作池"}
[EzC2]2025-07-31 11:46:59.643	info	数据库连接池管理器创建成功	{"maxOpenConns": 100, "maxIdleConns": 10, "connMaxLifetime": 3600, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:46:59.643	info	已配置JWT签名密钥，跳过自动生成
[EzC2]2025-07-31 11:46:59.644	info	组件初始化成功	{"component": "config_helper", "duration": 0.0047262}
[EzC2]2025-07-31 11:46:59.644	info	工作池创建成功	{"name": "网络操作池", "minWorkers": 20, "maxWorkers": 60, "queueSize": 1000}
[EzC2]2025-07-31 11:46:59.644	info	数据库连接池管理器已启动
[EzC2]2025-07-31 11:46:59.645	info	全局数据库连接池管理器初始化完成	{"maxOpenConns": 100, "maxIdleConns": 10, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:46:59.645	info	工作池创建成功	{"poolType": "network", "name": "网络操作池"}
[EzC2]2025-07-31 11:46:59.645	info	🚀 数据库连接池升级监控已启动
[EzC2]2025-07-31 11:46:59.646	info	组件初始化成功	{"component": "database", "duration": 0.0062894}
[EzC2]2025-07-31 11:46:59.645	info	工作池创建成功	{"name": "文件IO操作池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 300}
[EzC2]2025-07-31 11:46:59.646	info	工作池创建成功	{"poolType": "fileio", "name": "文件IO操作池"}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"name": "数据处理池", "minWorkers": 20, "maxWorkers": 80, "queueSize": 2000}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"poolType": "processing", "name": "数据处理池"}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"name": "心跳处理池", "minWorkers": 2, "maxWorkers": 8, "queueSize": 1000}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"poolType": "heartbeat", "name": "心跳处理池"}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"name": "通用任务池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 500}
[EzC2]2025-07-31 11:46:59.648	info	工作池创建成功	{"poolType": "general", "name": "通用任务池"}
[EzC2]2025-07-31 11:46:59.648	info	默认工作池初始化完成	{"CPU核心数": 20, "工作池数量": 6}
[EzC2]2025-07-31 11:46:59.649	info	组件初始化成功	{"component": "workerpool", "duration": 0.0089974}
[EzC2]2025-07-31 11:46:59.649	info	Goroutine泄漏检测器已启动	{"checkInterval": 30, "thresholdIncrease": 50, "maxGoroutines": 5000}
[EzC2]2025-07-31 11:46:59.649	info	组件初始化成功	{"component": "leak_detector", "duration": 0.0005223}
[EzC2]2025-07-31 11:46:59.649	info	开始初始化默认全局心跳配置...
[EzC2]2025-07-31 11:46:59.650	info	从数据库恢复代理信息	{"count": 0}
[EzC2]2025-07-31 11:46:59.650	info	组件初始化成功	{"component": "proxy_manager", "duration": 0.001182}
[EzC2]2025-07-31 11:46:59.650	info	全局心跳配置已存在，跳过初始化	{"id": 1, "interval": 30}
[EzC2]2025-07-31 11:46:59.651	info	组件初始化成功	{"component": "heartbeat_config", "duration": 0.0022649}
[EzC2]2025-07-31 11:46:59.649	info	客户端二进制存储目录: ./clientbin
[EzC2]2025-07-31 11:46:59.649	info	🔧 开始初始化客户端事件处理器...
[EzC2]2025-07-31 11:46:59.652	info	✅ 客户端事件处理器初始化完成
[EzC2]2025-07-31 11:46:59.653	info	组件初始化成功	{"component": "client_service", "duration": 0.0042685}
[EzC2]2025-07-31 11:46:59.652	info	组件初始化成功	{"component": "other_init", "duration": 0.0029859}
[EzC2]2025-07-31 11:46:59.660	info	注册结构体到数据库中成功
[EzC2]2025-07-31 11:46:59.661	info	组件初始化成功	{"component": "tables", "duration": 0.0114642}
[EzC2]2025-07-31 11:46:59.661	info	已启动 0 个pipe监听器
[EzC2]2025-07-31 11:46:59.661	info	组件初始化成功	{"component": "pipe_listeners", "duration": 0.0005348}
[EzC2]2025-07-31 11:46:59.662	info	已启动 1 个tcp监听器
[EzC2]2025-07-31 11:46:59.663	info	组件初始化成功	{"component": "tcp_listeners", "duration": 0.0022763}
[EzC2]2025-07-31 11:46:59.728	info	更新管理员账号成功: 
[EzC2]2025-07-31 11:46:59.728	info	Username: admin
[EzC2]2025-07-31 11:46:59.729	info	Password: ********* (已更新)
[EzC2]2025-07-31 11:46:59.729	info	系统初始化完成
[EzC2]2025-07-31 11:46:59.729	info	组件初始化成功	{"component": "sys_user", "duration": 0.068454}
[EzC2]2025-07-31 11:46:59.730	info	🚀 系统初始化完成	{"总耗时": 0.1124828, "组件数量": 17}
[EzC2]2025-07-31 11:46:59.735	info	离线检测服务已启动
