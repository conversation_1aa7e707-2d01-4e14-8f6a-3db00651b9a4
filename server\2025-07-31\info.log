[EzC2]2025-07-31 11:46:59.618	info	组件初始化成功	{"component": "zap", "duration": 0}
[EzC2]2025-07-31 11:46:59.619	info	组件初始化成功	{"component": "viper", "duration": 0.0015968}
[EzC2]2025-07-31 11:46:59.640	info	客户端事件管理器初始化完成
[EzC2]2025-07-31 11:46:59.640	info	智能缓存管理器创建成功	{"maxSize": 10000, "maxMemory": 104857600}
[EzC2]2025-07-31 11:46:59.641	info	全局智能缓存管理器初始化完成
[EzC2]2025-07-31 11:46:59.640	info	SSE管理器初始化完成
[EzC2]2025-07-31 11:46:59.640	info	未配置磁盘列表，将自动检测系统盘符
[EzC2]2025-07-31 11:46:59.640	info	工作池创建成功	{"name": "数据库操作池", "minWorkers": 2, "maxWorkers": 10, "queueSize": 500}
[EzC2]2025-07-31 11:46:59.640	info	组件初始化成功	{"component": "event_manager", "duration": 0.0005198}
[EzC2]2025-07-31 11:46:59.641	info	组件初始化成功	{"component": "smart_cache", "duration": 0.0010402}
[EzC2]2025-07-31 11:46:59.641	info	连接sqlite成功，已配置连接池参数	{"maxIdleConns": 10, "maxOpenConns": 100}
[EzC2]2025-07-31 11:46:59.641	info	组件初始化成功	{"component": "sse_manager", "duration": 0.0015576}
[EzC2]2025-07-31 11:46:59.642	info	自动检测到系统盘符: [C:\ E:\]
[EzC2]2025-07-31 11:46:59.642	info	工作池创建成功	{"poolType": "database", "name": "数据库操作池"}
[EzC2]2025-07-31 11:46:59.643	info	数据库连接池管理器创建成功	{"maxOpenConns": 100, "maxIdleConns": 10, "connMaxLifetime": 3600, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:46:59.643	info	已配置JWT签名密钥，跳过自动生成
[EzC2]2025-07-31 11:46:59.644	info	组件初始化成功	{"component": "config_helper", "duration": 0.0047262}
[EzC2]2025-07-31 11:46:59.644	info	工作池创建成功	{"name": "网络操作池", "minWorkers": 20, "maxWorkers": 60, "queueSize": 1000}
[EzC2]2025-07-31 11:46:59.644	info	数据库连接池管理器已启动
[EzC2]2025-07-31 11:46:59.645	info	全局数据库连接池管理器初始化完成	{"maxOpenConns": 100, "maxIdleConns": 10, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:46:59.645	info	工作池创建成功	{"poolType": "network", "name": "网络操作池"}
[EzC2]2025-07-31 11:46:59.645	info	🚀 数据库连接池升级监控已启动
[EzC2]2025-07-31 11:46:59.646	info	组件初始化成功	{"component": "database", "duration": 0.0062894}
[EzC2]2025-07-31 11:46:59.645	info	工作池创建成功	{"name": "文件IO操作池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 300}
[EzC2]2025-07-31 11:46:59.646	info	工作池创建成功	{"poolType": "fileio", "name": "文件IO操作池"}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"name": "数据处理池", "minWorkers": 20, "maxWorkers": 80, "queueSize": 2000}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"poolType": "processing", "name": "数据处理池"}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"name": "心跳处理池", "minWorkers": 2, "maxWorkers": 8, "queueSize": 1000}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"poolType": "heartbeat", "name": "心跳处理池"}
[EzC2]2025-07-31 11:46:59.647	info	工作池创建成功	{"name": "通用任务池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 500}
[EzC2]2025-07-31 11:46:59.648	info	工作池创建成功	{"poolType": "general", "name": "通用任务池"}
[EzC2]2025-07-31 11:46:59.648	info	默认工作池初始化完成	{"CPU核心数": 20, "工作池数量": 6}
[EzC2]2025-07-31 11:46:59.649	info	组件初始化成功	{"component": "workerpool", "duration": 0.0089974}
[EzC2]2025-07-31 11:46:59.649	info	Goroutine泄漏检测器已启动	{"checkInterval": 30, "thresholdIncrease": 50, "maxGoroutines": 5000}
[EzC2]2025-07-31 11:46:59.649	info	组件初始化成功	{"component": "leak_detector", "duration": 0.0005223}
[EzC2]2025-07-31 11:46:59.649	info	开始初始化默认全局心跳配置...
[EzC2]2025-07-31 11:46:59.650	info	从数据库恢复代理信息	{"count": 0}
[EzC2]2025-07-31 11:46:59.650	info	组件初始化成功	{"component": "proxy_manager", "duration": 0.001182}
[EzC2]2025-07-31 11:46:59.650	info	全局心跳配置已存在，跳过初始化	{"id": 1, "interval": 30}
[EzC2]2025-07-31 11:46:59.651	info	组件初始化成功	{"component": "heartbeat_config", "duration": 0.0022649}
[EzC2]2025-07-31 11:46:59.649	info	客户端二进制存储目录: ./clientbin
[EzC2]2025-07-31 11:46:59.649	info	🔧 开始初始化客户端事件处理器...
[EzC2]2025-07-31 11:46:59.652	info	✅ 客户端事件处理器初始化完成
[EzC2]2025-07-31 11:46:59.653	info	组件初始化成功	{"component": "client_service", "duration": 0.0042685}
[EzC2]2025-07-31 11:46:59.652	info	组件初始化成功	{"component": "other_init", "duration": 0.0029859}
[EzC2]2025-07-31 11:46:59.660	info	注册结构体到数据库中成功
[EzC2]2025-07-31 11:46:59.661	info	组件初始化成功	{"component": "tables", "duration": 0.0114642}
[EzC2]2025-07-31 11:46:59.661	info	已启动 0 个pipe监听器
[EzC2]2025-07-31 11:46:59.661	info	组件初始化成功	{"component": "pipe_listeners", "duration": 0.0005348}
[EzC2]2025-07-31 11:46:59.662	info	已启动 1 个tcp监听器
[EzC2]2025-07-31 11:46:59.663	info	组件初始化成功	{"component": "tcp_listeners", "duration": 0.0022763}
[EzC2]2025-07-31 11:46:59.728	info	更新管理员账号成功: 
[EzC2]2025-07-31 11:46:59.728	info	Username: admin
[EzC2]2025-07-31 11:46:59.729	info	Password: ********* (已更新)
[EzC2]2025-07-31 11:46:59.729	info	系统初始化完成
[EzC2]2025-07-31 11:46:59.729	info	组件初始化成功	{"component": "sys_user", "duration": 0.068454}
[EzC2]2025-07-31 11:46:59.730	info	🚀 系统初始化完成	{"总耗时": 0.1124828, "组件数量": 17}
[EzC2]2025-07-31 11:46:59.735	info	离线检测服务已启动
[EzC2]2025-07-31 11:47:41.250	info	组件初始化成功	{"component": "zap", "duration": 0}
[EzC2]2025-07-31 11:47:41.252	info	组件初始化成功	{"component": "viper", "duration": 0.0011557}
[EzC2]2025-07-31 11:47:41.262	info	客户端事件管理器初始化完成
[EzC2]2025-07-31 11:47:41.262	info	SSE管理器初始化完成
[EzC2]2025-07-31 11:47:41.262	info	未配置磁盘列表，将自动检测系统盘符
[EzC2]2025-07-31 11:47:41.262	info	工作池创建成功	{"name": "数据库操作池", "minWorkers": 2, "maxWorkers": 10, "queueSize": 500}
[EzC2]2025-07-31 11:47:41.264	info	工作池创建成功	{"poolType": "database", "name": "数据库操作池"}
[EzC2]2025-07-31 11:47:41.262	info	智能缓存管理器创建成功	{"maxSize": 10000, "maxMemory": 104857600}
[EzC2]2025-07-31 11:47:41.262	info	组件初始化成功	{"component": "event_manager", "duration": 0}
[EzC2]2025-07-31 11:47:41.263	info	组件初始化成功	{"component": "sse_manager", "duration": 0.0011272}
[EzC2]2025-07-31 11:47:41.264	info	自动检测到系统盘符: [C:\ E:\]
[EzC2]2025-07-31 11:47:41.264	info	连接sqlite成功，已配置连接池参数	{"maxIdleConns": 10, "maxOpenConns": 100}
[EzC2]2025-07-31 11:47:41.265	info	工作池创建成功	{"name": "网络操作池", "minWorkers": 20, "maxWorkers": 60, "queueSize": 1000}
[EzC2]2025-07-31 11:47:41.265	info	全局智能缓存管理器初始化完成
[EzC2]2025-07-31 11:47:41.266	info	已配置JWT签名密钥，跳过自动生成
[EzC2]2025-07-31 11:47:41.267	info	组件初始化成功	{"component": "config_helper", "duration": 0.0045037}
[EzC2]2025-07-31 11:47:41.266	info	数据库连接池管理器创建成功	{"maxOpenConns": 100, "maxIdleConns": 10, "connMaxLifetime": 3600, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:47:41.267	info	数据库连接池管理器已启动
[EzC2]2025-07-31 11:47:41.266	info	工作池创建成功	{"poolType": "network", "name": "网络操作池"}
[EzC2]2025-07-31 11:47:41.266	info	组件初始化成功	{"component": "smart_cache", "duration": 0.0039913}
[EzC2]2025-07-31 11:47:41.267	info	全局数据库连接池管理器初始化完成	{"maxOpenConns": 100, "maxIdleConns": 10, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:47:41.268	info	工作池创建成功	{"name": "文件IO操作池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 300}
[EzC2]2025-07-31 11:47:41.268	info	工作池创建成功	{"poolType": "fileio", "name": "文件IO操作池"}
[EzC2]2025-07-31 11:47:41.268	info	🚀 数据库连接池升级监控已启动
[EzC2]2025-07-31 11:47:41.268	info	工作池创建成功	{"name": "数据处理池", "minWorkers": 20, "maxWorkers": 80, "queueSize": 2000}
[EzC2]2025-07-31 11:47:41.269	info	工作池创建成功	{"poolType": "processing", "name": "数据处理池"}
[EzC2]2025-07-31 11:47:41.269	info	组件初始化成功	{"component": "database", "duration": 0.0066604}
[EzC2]2025-07-31 11:47:41.269	info	工作池创建成功	{"name": "心跳处理池", "minWorkers": 2, "maxWorkers": 8, "queueSize": 1000}
[EzC2]2025-07-31 11:47:41.270	info	工作池创建成功	{"poolType": "heartbeat", "name": "心跳处理池"}
[EzC2]2025-07-31 11:47:41.270	info	工作池创建成功	{"name": "通用任务池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 500}
[EzC2]2025-07-31 11:47:41.271	info	工作池创建成功	{"poolType": "general", "name": "通用任务池"}
[EzC2]2025-07-31 11:47:41.271	info	默认工作池初始化完成	{"CPU核心数": 20, "工作池数量": 6}
[EzC2]2025-07-31 11:47:41.271	info	组件初始化成功	{"component": "workerpool", "duration": 0.009243}
[EzC2]2025-07-31 11:47:41.272	info	🔧 开始初始化客户端事件处理器...
[EzC2]2025-07-31 11:47:41.272	info	客户端二进制存储目录: ./clientbin
[EzC2]2025-07-31 11:47:41.272	info	Goroutine泄漏检测器已启动	{"checkInterval": 30, "thresholdIncrease": 50, "maxGoroutines": 5000}
[EzC2]2025-07-31 11:47:41.273	info	组件初始化成功	{"component": "leak_detector", "duration": 0.0005564}
[EzC2]2025-07-31 11:47:41.272	info	✅ 客户端事件处理器初始化完成
[EzC2]2025-07-31 11:47:41.273	info	组件初始化成功	{"component": "client_service", "duration": 0.0010741}
[EzC2]2025-07-31 11:47:41.272	info	开始初始化默认全局心跳配置...
[EzC2]2025-07-31 11:47:41.273	info	组件初始化成功	{"component": "other_init", "duration": 0.0005564}
[EzC2]2025-07-31 11:47:41.273	info	从数据库恢复代理信息	{"count": 0}
[EzC2]2025-07-31 11:47:41.275	info	组件初始化成功	{"component": "proxy_manager", "duration": 0.0037166}
[EzC2]2025-07-31 11:47:41.276	info	全局心跳配置已存在，跳过初始化	{"id": 1, "interval": 30}
[EzC2]2025-07-31 11:47:41.276	info	组件初始化成功	{"component": "heartbeat_config", "duration": 0.0042409}
[EzC2]2025-07-31 11:47:41.282	info	注册结构体到数据库中成功
[EzC2]2025-07-31 11:47:41.282	info	组件初始化成功	{"component": "tables", "duration": 0.0101424}
[EzC2]2025-07-31 11:47:41.283	info	已启动 0 个pipe监听器
[EzC2]2025-07-31 11:47:41.283	info	组件初始化成功	{"component": "pipe_listeners", "duration": 0.0005219}
[EzC2]2025-07-31 11:47:41.284	info	心跳管理器已启动
[EzC2]2025-07-31 11:47:41.285	info	RSA密钥对初始化成功	{"listenerID": 1}
[EzC2]2025-07-31 11:47:41.285	info	TCP监听器 1 已启动在 0.0.0.0:9999，客户端文件名前缀: 81e2282ffd80771a
[EzC2]2025-07-31 11:47:41.285	info	已启动 1 个tcp监听器
[EzC2]2025-07-31 11:47:41.286	info	组件初始化成功	{"component": "tcp_listeners", "duration": 0.0031411}
[EzC2]2025-07-31 11:47:41.285	info	找到平台目录 template/windows，包含 4 个条目
[EzC2]2025-07-31 11:47:41.286	info	找到平台目录 template/linux，包含 3 个条目
[EzC2]2025-07-31 11:47:41.343	info	更新管理员账号成功: 
[EzC2]2025-07-31 11:47:41.343	info	Username: admin
[EzC2]2025-07-31 11:47:41.343	info	Password: ********* (已更新)
[EzC2]2025-07-31 11:47:41.343	info	系统初始化完成
[EzC2]2025-07-31 11:47:41.344	info	组件初始化成功	{"component": "sys_user", "duration": 0.0613399}
[EzC2]2025-07-31 11:47:41.344	info	🚀 系统初始化完成	{"总耗时": 0.0935821, "组件数量": 17}
[EzC2]2025-07-31 11:47:41.351	info	离线检测服务已启动
[EzC2]2025-07-31 11:47:43.108	info	成功生成 windows/amd64 客户端，保存到: clientbin\81e2282ffd80771a_windows_amd64.exe，大小: 11199488 字节
[EzC2]2025-07-31 11:47:44.003	info	📡 通知SSE连接已建立	{"userID": 1}
[EzC2]2025-07-31 11:47:44.004	info	新增SSE连接	{"userID": 1, "remoteAddr": "[::1]:50184", "totalConnections": 1}
[EzC2]2025-07-31 11:47:44.005	info	✅ SSE连接已注册到管理器	{"userID": 1}
[EzC2]2025-07-31 11:47:44.005	info	开始推送客户端心跳数据流	{"clientID": 1}
[EzC2]2025-07-31 11:47:44.606	info	Dashboard SSE连接已建立
[EzC2]2025-07-31 11:47:44.723	info	成功生成 windows/386 客户端，保存到: clientbin\81e2282ffd80771a_windows_386.exe，大小: 10922496 字节
[EzC2]2025-07-31 11:47:46.457	info	成功生成 windows/arm64 客户端，保存到: clientbin\81e2282ffd80771a_windows_arm64.exe，大小: 10397696 字节
[EzC2]2025-07-31 11:47:46.463	info	Linux客户端生成成功
[EzC2]2025-07-31 11:47:46.463	info	找到平台目录 template/darwin，包含 3 个条目
[EzC2]2025-07-31 11:47:47.969	info	成功生成 windows/arm 客户端，保存到: clientbin\81e2282ffd80771a_windows_arm.exe，大小: 10619392 字节
[EzC2]2025-07-31 11:47:47.980	info	Windows客户端生成成功
[EzC2]2025-07-31 11:47:47.986	info	Darwin客户端生成成功
[EzC2]2025-07-31 11:48:11.267	info	数据库连接池自动缩容	{"oldMaxOpen": 100, "newMaxOpen": 98, "newMaxIdle": 29, "utilization": 0}
[EzC2]2025-07-31 11:48:18.661	info	关闭主Server
[EzC2]2025-07-31 11:48:18.661	info	触发全局关闭信号
[EzC2]2025-07-31 11:48:18.662	info	数据库连接池管理器已停止
[EzC2]2025-07-31 11:48:18.662	info	全局数据库连接池管理器已停止
[EzC2]2025-07-31 11:48:18.663	info	收到服务器关闭信号，停止Dashboard SSE推送
[EzC2]2025-07-31 11:48:18.663	info	收到服务器关闭信号，停止SSE推送	{"clientID": 1}
[EzC2]2025-07-31 11:48:18.663	info	收到服务器关闭信号，停止离线检测服务
[EzC2]2025-07-31 11:48:18.665	info	所有goroutine已优雅关闭
[EzC2]2025-07-31 11:48:18.665	info	关闭SSE连接	{"userID": 1}
[EzC2]2025-07-31 11:48:18.665	info	SSE连接上下文已取消	{"userID": 1}
[EzC2]2025-07-31 11:48:18.684	info	WEB服务已关闭
[EzC2]2025-07-31 11:48:18.684	info	停止Proxy同步服务
[EzC2]2025-07-31 11:48:18.684	info	服务器关闭时将所有在线客户端设置为离线	{"affected_count": 0}
[EzC2]2025-07-31 11:48:18.685	info	成功将所有在线客户端设置为离线	{"count": 0}
[EzC2]2025-07-31 11:48:18.686	info	数据库连接已关闭
[EzC2]2025-07-31 11:48:18.687	info	成功删除客户端文件: clientbin\81e2282ffd80771a_windows_386.exe
[EzC2]2025-07-31 11:48:18.688	info	成功删除客户端文件: clientbin\81e2282ffd80771a_windows_amd64.exe
[EzC2]2025-07-31 11:48:18.690	info	成功删除客户端文件: clientbin\81e2282ffd80771a_windows_arm.exe
[EzC2]2025-07-31 11:48:18.692	info	成功删除客户端文件: clientbin\81e2282ffd80771a_windows_arm64.exe
[EzC2]2025-07-31 11:49:00.591	info	组件初始化成功	{"component": "zap", "duration": 0}
[EzC2]2025-07-31 11:49:00.592	info	组件初始化成功	{"component": "viper", "duration": 0.0015505}
[EzC2]2025-07-31 11:49:00.601	info	未配置磁盘列表，将自动检测系统盘符
[EzC2]2025-07-31 11:49:00.602	info	自动检测到系统盘符: [C:\ E:\]
[EzC2]2025-07-31 11:49:00.601	info	工作池创建成功	{"name": "数据库操作池", "minWorkers": 2, "maxWorkers": 10, "queueSize": 500}
[EzC2]2025-07-31 11:49:00.601	info	客户端事件管理器初始化完成
[EzC2]2025-07-31 11:49:00.601	info	SSE管理器初始化完成
[EzC2]2025-07-31 11:49:00.601	info	智能缓存管理器创建成功	{"maxSize": 10000, "maxMemory": 104857600}
[EzC2]2025-07-31 11:49:00.602	info	已配置JWT签名密钥，跳过自动生成
[EzC2]2025-07-31 11:49:00.603	info	工作池创建成功	{"poolType": "database", "name": "数据库操作池"}
[EzC2]2025-07-31 11:49:00.603	info	连接sqlite成功，已配置连接池参数	{"maxIdleConns": 10, "maxOpenConns": 100}
[EzC2]2025-07-31 11:49:00.603	info	组件初始化成功	{"component": "event_manager", "duration": 0.0016307}
[EzC2]2025-07-31 11:49:00.603	info	组件初始化成功	{"component": "sse_manager", "duration": 0.0021787}
[EzC2]2025-07-31 11:49:00.603	info	全局智能缓存管理器初始化完成
[EzC2]2025-07-31 11:49:00.604	info	组件初始化成功	{"component": "config_helper", "duration": 0.0027506}
[EzC2]2025-07-31 11:49:00.604	info	工作池创建成功	{"name": "网络操作池", "minWorkers": 20, "maxWorkers": 60, "queueSize": 1000}
[EzC2]2025-07-31 11:49:00.604	info	数据库连接池管理器创建成功	{"maxOpenConns": 100, "maxIdleConns": 10, "connMaxLifetime": 3600, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:49:00.606	info	组件初始化成功	{"component": "smart_cache", "duration": 0.0044113}
[EzC2]2025-07-31 11:49:00.606	info	工作池创建成功	{"poolType": "network", "name": "网络操作池"}
[EzC2]2025-07-31 11:49:00.606	info	数据库连接池管理器已启动
[EzC2]2025-07-31 11:49:00.607	info	全局数据库连接池管理器初始化完成	{"maxOpenConns": 100, "maxIdleConns": 10, "healthCheckInterval": 30}
[EzC2]2025-07-31 11:49:00.607	info	🚀 数据库连接池升级监控已启动
[EzC2]2025-07-31 11:49:00.607	info	工作池创建成功	{"name": "文件IO操作池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 300}
[EzC2]2025-07-31 11:49:00.608	info	组件初始化成功	{"component": "database", "duration": 0.0070178}
[EzC2]2025-07-31 11:49:00.608	info	工作池创建成功	{"poolType": "fileio", "name": "文件IO操作池"}
[EzC2]2025-07-31 11:49:00.609	info	工作池创建成功	{"name": "数据处理池", "minWorkers": 20, "maxWorkers": 80, "queueSize": 2000}
[EzC2]2025-07-31 11:49:00.609	info	工作池创建成功	{"poolType": "processing", "name": "数据处理池"}
[EzC2]2025-07-31 11:49:00.610	info	工作池创建成功	{"name": "心跳处理池", "minWorkers": 2, "maxWorkers": 8, "queueSize": 1000}
[EzC2]2025-07-31 11:49:00.610	info	工作池创建成功	{"poolType": "heartbeat", "name": "心跳处理池"}
[EzC2]2025-07-31 11:49:00.610	info	工作池创建成功	{"name": "通用任务池", "minWorkers": 2, "maxWorkers": 40, "queueSize": 500}
[EzC2]2025-07-31 11:49:00.610	info	工作池创建成功	{"poolType": "general", "name": "通用任务池"}
[EzC2]2025-07-31 11:49:00.610	info	默认工作池初始化完成	{"CPU核心数": 20, "工作池数量": 6}
[EzC2]2025-07-31 11:49:00.611	info	组件初始化成功	{"component": "workerpool", "duration": 0.0096567}
[EzC2]2025-07-31 11:49:00.611	info	客户端二进制存储目录: ./clientbin
[EzC2]2025-07-31 11:49:00.611	info	组件初始化成功	{"component": "other_init", "duration": 0.000515}
[EzC2]2025-07-31 11:49:00.611	info	开始初始化默认全局心跳配置...
[EzC2]2025-07-31 11:49:00.611	info	🔧 开始初始化客户端事件处理器...
[EzC2]2025-07-31 11:49:00.611	info	Goroutine泄漏检测器已启动	{"checkInterval": 30, "thresholdIncrease": 50, "maxGoroutines": 5000}
[EzC2]2025-07-31 11:49:00.613	info	组件初始化成功	{"component": "leak_detector", "duration": 0.0018179}
[EzC2]2025-07-31 11:49:00.612	info	从数据库恢复代理信息	{"count": 0}
[EzC2]2025-07-31 11:49:00.613	info	全局心跳配置已存在，跳过初始化	{"id": 1, "interval": 30}
[EzC2]2025-07-31 11:49:00.613	info	✅ 客户端事件处理器初始化完成
[EzC2]2025-07-31 11:49:00.615	info	组件初始化成功	{"component": "proxy_manager", "duration": 0.0042639}
[EzC2]2025-07-31 11:49:00.616	info	组件初始化成功	{"component": "heartbeat_config", "duration": 0.004274}
[EzC2]2025-07-31 11:49:00.616	info	组件初始化成功	{"component": "client_service", "duration": 0.004843}
[EzC2]2025-07-31 11:49:00.622	info	注册结构体到数据库中成功
[EzC2]2025-07-31 11:49:00.623	info	组件初始化成功	{"component": "tables", "duration": 0.0121379}
[EzC2]2025-07-31 11:49:00.624	info	心跳管理器已启动
[EzC2]2025-07-31 11:49:00.624	info	RSA密钥对初始化成功	{"listenerID": 1}
[EzC2]2025-07-31 11:49:00.625	info	已启动 0 个pipe监听器
[EzC2]2025-07-31 11:49:00.625	info	组件初始化成功	{"component": "pipe_listeners", "duration": 0.0015908}
[EzC2]2025-07-31 11:49:00.625	info	找到平台目录 template/windows，包含 4 个条目
[EzC2]2025-07-31 11:49:00.625	info	TCP监听器 1 已启动在 0.0.0.0:9999，客户端文件名前缀: 8ef0f9dbdf74c4fe
[EzC2]2025-07-31 11:49:00.627	info	已启动 1 个tcp监听器
[EzC2]2025-07-31 11:49:00.625	info	找到平台目录 template/linux，包含 3 个条目
[EzC2]2025-07-31 11:49:00.628	info	组件初始化成功	{"component": "tcp_listeners", "duration": 0.0040784}
[EzC2]2025-07-31 11:49:00.691	info	更新管理员账号成功: 
[EzC2]2025-07-31 11:49:00.692	info	Username: admin
[EzC2]2025-07-31 11:49:00.692	info	Password: ********* (已更新)
[EzC2]2025-07-31 11:49:00.693	info	系统初始化完成
[EzC2]2025-07-31 11:49:00.694	info	组件初始化成功	{"component": "sys_user", "duration": 0.0699994}
[EzC2]2025-07-31 11:49:00.694	info	🚀 系统初始化完成	{"总耗时": 0.103273, "组件数量": 17}
[EzC2]2025-07-31 11:49:00.702	info	离线检测服务已启动
[EzC2]2025-07-31 11:49:00.967	info	开始推送客户端心跳数据流	{"clientID": 1}
[EzC2]2025-07-31 11:49:00.967	info	📡 通知SSE连接已建立	{"userID": 1}
[EzC2]2025-07-31 11:49:00.968	info	新增SSE连接	{"userID": 1, "remoteAddr": "127.0.0.1:50380", "totalConnections": 1}
[EzC2]2025-07-31 11:49:00.969	info	✅ SSE连接已注册到管理器	{"userID": 1}
[EzC2]2025-07-31 11:49:01.336	info	Dashboard SSE连接已建立
[EzC2]2025-07-31 11:49:02.524	info	成功生成 windows/amd64 客户端，保存到: clientbin\8ef0f9dbdf74c4fe_windows_amd64.exe，大小: 11199488 字节
[EzC2]2025-07-31 11:49:04.267	info	成功生成 windows/386 客户端，保存到: clientbin\8ef0f9dbdf74c4fe_windows_386.exe，大小: 10922496 字节
[EzC2]2025-07-31 11:49:05.742	info	成功生成 windows/arm64 客户端，保存到: clientbin\8ef0f9dbdf74c4fe_windows_arm64.exe，大小: 10397696 字节
[EzC2]2025-07-31 11:49:06.079	info	Linux客户端生成成功
[EzC2]2025-07-31 11:49:06.080	info	找到平台目录 template/darwin，包含 3 个条目
[EzC2]2025-07-31 11:49:07.179	info	成功生成 windows/arm 客户端，保存到: clientbin\8ef0f9dbdf74c4fe_windows_arm.exe，大小: 10619392 字节
[EzC2]2025-07-31 11:49:07.198	info	Windows客户端生成成功
[EzC2]2025-07-31 11:49:07.533	info	Darwin客户端生成成功
