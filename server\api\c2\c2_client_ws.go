package c2

import (
	"encoding/json"
	"fmt"
	"net/http"
	"server/core/manager"
	"server/core/manager/shutdown"
	"server/global"
	"server/model/sys"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

//本文件用于前端和后端的websocket通信，起到对客户端进行控制的作用

// WebSocket连接升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源的WebSocket连接
		return true
	},
}

// 客户端WebSocket连接管理
type ClientWebSocketManager struct {
	connections  map[uint][]*websocket.Conn // 客户端ID -> WebSocket连接列表
	MessageStack map[uint][]interface{}
	mutex        sync.RWMutex
}

// 全局WebSocket连接管理器
var clientWSManager = ClientWebSocketManager{
	connections:  make(map[uint][]*websocket.Conn),
	MessageStack: make(map[uint][]interface{}),
}

// 注册WebSocket连接
func (m *ClientWebSocketManager) RegisterConnection(clientID uint, conn *websocket.Conn) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 添加到连接列表
	m.connections[clientID] = append(m.connections[clientID], conn)

	if len(m.MessageStack[clientID]) != 0 {
		for _, msg := range m.MessageStack[clientID] {
			mes := msg.(map[string]string)
			global.LOG.Info("发送积累的消息: " + mes["output"])
			err := conn.WriteJSON(msg)
			if err != nil {
				global.LOG.Error(fmt.Sprintf("发送WebSocket消息失败: %s", err.Error()))
			}
		}
	}
	delete(m.MessageStack, clientID)
	global.LOG.Info(fmt.Sprintf("注册WebSocket连接，客户端ID: %d", clientID))
}

// 移除WebSocket连接
func (m *ClientWebSocketManager) RemoveConnection(clientID uint, conn *websocket.Conn) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	conns, exists := m.connections[clientID]
	if !exists {
		return
	}

	// 查找并移除连接
	for i, c := range conns {
		if c == conn {
			m.connections[clientID] = append(conns[:i], conns[i+1:]...)
			break
		}
	}

	// 如果没有连接了，删除客户端条目
	if len(m.connections[clientID]) == 0 {
		delete(m.connections, clientID)
	}

	global.LOG.Info(fmt.Sprintf("移除WebSocket连接，客户端ID: %d", clientID))
}

// 向客户端发送消息
func (m *ClientWebSocketManager) SendMessage(clientID uint, message interface{}) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	conns, exists := m.connections[clientID]

	if !exists || len(conns) == 0 {
		global.LOG.Info("向前端发送消息，前端WS连接不存在")
		m.MessageStack[clientID] = append(m.MessageStack[clientID], message)
		return
	}

	// 向所有连接发送消息
	for _, conn := range conns {
		if len(m.MessageStack[clientID]) != 0 {
			for _, msg := range m.MessageStack[clientID] {
				mes := msg.(map[string]string)
				global.LOG.Info("发送积累的消息: " + mes["output"])
				err := conn.WriteJSON(msg)
				if err != nil {
					global.LOG.Error(fmt.Sprintf("发送WebSocket消息失败: %s", err.Error()))
				}
			}
		}
		delete(m.MessageStack, clientID)
		err := conn.WriteJSON(message)
		if err != nil {
			m.MessageStack[clientID] = append(m.MessageStack[clientID], message)
			global.LOG.Error(fmt.Sprintf("发送WebSocket消息失败: %s", err.Error()))
		}
	}
}

// 处理WebSocket连接
func (c *ClientApi) HandleWebSocket(ctx *gin.Context) {
	// 验证token
	token := ctx.Query("token")
	if token == "" {
		ctx.String(http.StatusUnauthorized, "未提供认证信息")
		return
	}

	// 验证token有效性
	j := sys.NewJWT()
	_, err := j.ParseToken(token)
	if err != nil {
		global.LOG.Error("WebSocket连接认证失败", zap.Error(err))
		ctx.String(http.StatusUnauthorized, "认证失败")
		return
	}

	// 获取客户端ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.String(http.StatusBadRequest, "无效的客户端ID")
		return
	}

	// 检查客户端是否存在
	client, err := clientService.GetClient(uint(id))
	if err != nil {
		global.LOG.Error("获取客户端失败", zap.Error(err))
		ctx.String(http.StatusNotFound, "客户端不存在")
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		global.LOG.Error("升级WebSocket连接失败", zap.Error(err))
		return
	}

	// 注册WebSocket连接
	clientWSManager.RegisterConnection(client.ID, conn)

	// 启动心跳检测
	go handleHeartbeat(client.ID, conn)

	// 处理WebSocket消息
	go handleMessages(client.ID, conn)
}

// 处理心跳检测
func handleHeartbeat(clientID uint, conn *websocket.Conn) {
	ticker := time.NewTicker(30 * time.Second)
	defer func() {
		ticker.Stop()
		conn.Close()
		clientWSManager.RemoveConnection(clientID, conn)
	}()

	for {
		select {
		case <-ticker.C:
			// 发送ping消息
			if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				global.LOG.Error(fmt.Sprintf("发送心跳消息失败: %s", err.Error()))
				return
			}
		}
	}
}

// 处理WebSocket消息
func handleMessages(clientID uint, conn *websocket.Conn) {
	// 🚀 创建done channel来控制生命周期
	done := make(chan struct{})

	// 🚀 注册goroutine到关闭管理器
	shutdown.RegisterGoroutine()

	defer func() {
		shutdown.UnregisterGoroutine()
		close(done) // 确保所有goroutine退出
		conn.Close()
		clientWSManager.RemoveConnection(clientID, conn)
		// 清理该客户端的命令输出缓存
		manager.ResponseMgr.ClearCommandOutputs(clientID)
	}()

	// 设置消息处理函数
	conn.SetPongHandler(func(string) error {
		// 收到pong消息，更新最后活动时间
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 🚀 异步读取消息循环
	go func() {
		defer func() {
			// 发送完成信号
			select {
			case done <- struct{}{}:
			default:
			}
		}()

		for {
			conn.SetReadDeadline(time.Now().Add(60 * time.Second))

			msgType, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					global.LOG.Error(fmt.Sprintf("WebSocket连接异常关闭: %s", err.Error()))
				}
				return // 退出goroutine
			}

			// 🚀 异步处理消息，避免阻塞读取循环
			go processMessage(clientID, msgType, message)
		}
	}()

	// 🚀 新增：异步读取命令输出并发送到前端
	go func() {
		ticker := time.NewTicker(100 * time.Millisecond) // 每100ms检查一次
		defer ticker.Stop()

		for {
			select {
			case <-done:
				return // 连接关闭，退出
			case <-shutdown.GetShutdownChannel():
				global.LOG.Info("收到服务器关闭信号，停止WebSocket命令输出推送", zap.Uint("clientID", clientID))
				return // 服务器关闭，退出
			case <-ticker.C:
				// 从ResponseManager获取命令输出
				if outputs, found := manager.ResponseMgr.PopCommandOutputs(clientID); found {
					// 发送所有输出到前端
					for _, output := range outputs {
						message := map[string]interface{}{
							"code":    "200",
							"type":    output.Type,
							"content": output.Output,
						}

						// 解析终端ID（如果是多终端格式）
						if strings.HasPrefix(output.Type, "command_output_terminal_") {
							terminalIDStr := strings.TrimPrefix(output.Type, "command_output_terminal_")
							if terminalID, err := strconv.ParseUint(terminalIDStr, 10, 32); err == nil {
								message["terminal_id"] = uint32(terminalID)
								message["type"] = "command_output" // 统一类型
							}
						}

						if err := conn.WriteJSON(message); err != nil {
							global.LOG.Error("发送命令输出失败",
								zap.Uint("clientID", clientID),
								zap.Error(err))
							// 连接出错，退出
							select {
							case done <- struct{}{}:
							default:
							}
							return
						}
					}

					global.LOG.Debug("发送命令输出到前端",
						zap.Uint("clientID", clientID),
						zap.Int("outputCount", len(outputs)))
				}
			}
		}
	}()

	// 🚀 等待连接关闭
	<-done
}

// 🚀 新增：异步处理单个消息
func processMessage(clientID uint, msgType int, message []byte) {
	// 处理接收到的消息
	if msgType == websocket.TextMessage || msgType == websocket.BinaryMessage {
		// 处理resize消息
		messageStr := string(message)
		if strings.Contains(messageStr, "action") && strings.Contains(messageStr, "resize") {
			//{"action":"resize","terminal_id":0,"cols":185,"rows":33}
			type ResizeMessage struct {
				Action     string `json:"action"`
				TerminalID uint32 `json:"terminal_id"`
				Cols       uint16 `json:"cols"`
				Rows       uint16 `json:"rows"`
			}
			resizeMessage := &ResizeMessage{}
			if err := json.Unmarshal(message, &resizeMessage); err != nil {
				global.LOG.Error("解析Resize消息失败", zap.Error(err))
				return
			}

			// 发送resize命令到指定终端
			resizeCmd := fmt.Sprintf("RESIZE:%d:%d", resizeMessage.Cols, resizeMessage.Rows)
			err := commandService.SendCommandToTerminal(clientID, resizeCmd, resizeMessage.TerminalID)
			if err != nil {
				global.LOG.Error("发送Resize命令失败", zap.Error(err))
			}
			return
		}
		// 尝试解析为多终端命令格式
		type TerminalCommandMessage struct {
			TerminalID uint32 `json:"terminal_id,omitempty"`
			Command    string `json:"command,omitempty"`
			Action     string `json:"action,omitempty"`
		}

		var terminalCmd TerminalCommandMessage
		if err := json.Unmarshal(message, &terminalCmd); err == nil {
			// 处理不同的动作
			switch terminalCmd.Action {

			case "resize":
				// 终端大小调整 - 这种情况不应该出现，因为resize消息已经在上面单独处理了
				global.LOG.Warn("收到意外的resize命令格式", zap.Uint("clientID", clientID))

			default:
				// 普通命令
				if terminalCmd.Command != "" {
					terminalID := terminalCmd.TerminalID // 默认为0（主终端）
					if err := commandService.SendCommandToTerminal(clientID, terminalCmd.Command, terminalID); err != nil {
						global.LOG.Error(fmt.Sprintf("转发多终端命令失败: %s", err.Error()))
					}
				}
			}
		} else {
			// 旧格式：直接发送到主终端（向后兼容）
			if err := commandService.SendCommand(clientID, string(message)); err != nil {
				global.LOG.Error(fmt.Sprintf("转发输入到客户端失败: %s", err.Error()))
			}
		}
	}
}

// 发送响应消息给客户端前端
func sendResponseToClient(clientID uint, response interface{}) {
	responseData, err := json.Marshal(response)
	if err != nil {
		global.LOG.Error("序列化响应消息失败", zap.Error(err))
		return
	}

	// 获取客户端的WebSocket连接
	clientWSManager.mutex.RLock()
	connections, exists := clientWSManager.connections[clientID]
	clientWSManager.mutex.RUnlock()

	if !exists || len(connections) == 0 {
		global.LOG.Warn("未找到客户端WebSocket连接", zap.Uint("clientID", clientID))
		return
	}

	// 发送给所有连接
	for _, conn := range connections {
		if err := conn.WriteMessage(websocket.TextMessage, responseData); err != nil {
			global.LOG.Error("发送响应消息失败", zap.Error(err))
		}
	}
}
