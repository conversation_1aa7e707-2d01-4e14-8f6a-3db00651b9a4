package sys

import (
	"fmt"
	"runtime"
	"server/core/manager/dbpool"
	"server/core/manager/filetransferpool"
	"server/core/manager"
	"server/core/manager/workerpool"
	"server/global"
	"server/model/response"
	"server/model/task"
	"server/service"
	"server/service/sys"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PerformanceApi struct{}

// GetListenerPerformanceStats 获取监听器性能统计
func (p *PerformanceApi) GetListenerPerformanceStats(ctx *gin.Context) {
	listenerService := sys.ListenerPerformanceService{}
	responseData := listenerService.GetListenerPerformanceStats()

	response.OkWithData(responseData, ctx)
}

// GetListenerConnectionStats 获取监听器连接统计
func (p *PerformanceApi) GetListenerConnectionStats(ctx *gin.Context) {
	listenerService := sys.ListenerPerformanceService{}
	responseData := listenerService.GetListenerConnectionStats()

	response.OkWithData(responseData, ctx)
}

// GetListenerTrafficStats 获取监听器网络流量统计
func (p *PerformanceApi) GetListenerTrafficStats(ctx *gin.Context) {
	listenerService := sys.ListenerPerformanceService{}
	responseData := listenerService.GetListenerTrafficStats()

	response.OkWithData(responseData, ctx)
}

// GetClientConnectionStats 获取客户端连接性能统计
func (p *PerformanceApi) GetClientConnectionStats(ctx *gin.Context) {
	// 通过服务层获取客户端连接统计
	clientService := service.ServiceGroupManagerAPP.C2ServiceGroup.ClientService
	clientStats := clientService.GetClientConnectionStats()
	
	responseData := gin.H{
		"client_stats": clientStats,
		"timestamp": time.Now(),
	}
	
	response.OkWithData(responseData, ctx)
}

// GetClientTaskStats 获取客户端任务执行统计
func (p *PerformanceApi) GetClientTaskStats(ctx *gin.Context) {
	// 获取各种任务的执行统计
	taskStats := gin.H{
		"file_transfer_tasks": getFileTransferTaskStats(),
		"screenshot_tasks":    getScreenshotTaskStats(),
		"process_tasks":       getProcessTaskStats(),
		"network_tasks":       getNetworkTaskStats(),
	}
	
	responseData := gin.H{
		"task_stats": taskStats,
		"timestamp": time.Now(),
	}
	
	response.OkWithData(responseData, ctx)
}

// GetTaskExecutionStats 获取任务执行统计
func (p *PerformanceApi) GetTaskExecutionStats(ctx *gin.Context) {
	// 获取任务执行的详细统计
	executionStats := gin.H{
		"total_tasks_executed":    getTotalTasksExecuted(),
		"tasks_by_type":          getTasksByType(),
		"average_execution_time": getAverageExecutionTime(),
		"success_rate":           getTaskSuccessRate(),
		"failed_tasks":           getFailedTasksCount(),
	}
	
	responseData := gin.H{
		"execution_stats": executionStats,
		"timestamp": time.Now(),
	}
	
	response.OkWithData(responseData, ctx)
}

// GetTaskQueueStats 获取任务队列状态
func (p *PerformanceApi) GetTaskQueueStats(ctx *gin.Context) {
	queueStats := gin.H{
		"pending_tasks":    getPendingTasksCount(),
		"running_tasks":    getRunningTasksCount(),
		"completed_tasks":  getCompletedTasksCount(),
		"queue_length":     getQueueLength(),
		"processing_rate":  getProcessingRate(),
	}
	
	responseData := gin.H{
		"queue_stats": queueStats,
		"timestamp": time.Now(),
	}
	
	response.OkWithData(responseData, ctx)
}

// GetAllMemoryPoolStats 获取所有内存池统计
func (p *PerformanceApi) GetAllMemoryPoolStats(ctx *gin.Context) {
	// 获取文件传输内存池统计
	fileTransferStats := filetransferpool.GetStats()
	
	// 获取客户端内存池统计（如果有的话）
	clientMemoryStats := getClientMemoryPoolStats()
	
	responseData := gin.H{
		"filetransferpool": gin.H{
			"stats": fileTransferStats,
			"memory_usage_mb": float64(filetransferpool.GetMemoryUsageEstimate()) / (1024 * 1024),
			"efficiency_ratio": filetransferpool.GetEfficiencyRatio(),
		},
		"client_memory_pools": clientMemoryStats,
		"timestamp": time.Now(),
	}
	
	response.OkWithData(responseData, ctx)
}

// 辅助函数 - 获取文件传输任务统计
func getFileTransferTaskStats() gin.H {
	var totalTransfers, activeTransfers, completedTransfers, failedTransfers int64
	var avgSpeed float64

	// 🚀 从数据库获取实际统计
	var completedTasks []task.FileTransferTask
	dbpool.ExecuteDBOperationAsyncAndWait("file_transfer_stats", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Count(&totalTransfers)
		db.Model(&task.FileTransferTask{}).Where("status = ?", "running").Count(&activeTransfers)
		db.Model(&task.FileTransferTask{}).Where("status = ?", "completed").Count(&completedTransfers)
		db.Model(&task.FileTransferTask{}).Where("status = ?", "failed").Count(&failedTransfers)

		// 计算平均速度（基于已完成的任务）
		db.Where("status = ? AND completed_at IS NOT NULL AND started_at IS NOT NULL", "completed").
			Find(&completedTasks)
		return nil
	})

	if len(completedTasks) > 0 {
		var totalSpeed float64
		for _, t := range completedTasks {
			if t.CompletedAt != nil && t.StartedAt != nil {
				duration := t.CompletedAt.Sub(*t.StartedAt).Seconds()
				if duration > 0 && t.FileSize > 0 {
					speedMbps := float64(t.FileSize) / (1024 * 1024) / duration
					totalSpeed += speedMbps
				}
			}
		}
		avgSpeed = totalSpeed / float64(len(completedTasks))
	}

	return gin.H{
		"total_transfers":     totalTransfers,
		"active_transfers":    activeTransfers,
		"completed_transfers": completedTransfers,
		"failed_transfers":    failedTransfers,
		"average_speed_mbps":  avgSpeed,
	}
}

// 辅助函数 - 获取截图任务统计
func getScreenshotTaskStats() gin.H {
	var totalScreenshots, activeScreenshots, completedScreenshots, failedScreenshots int64
	var avgSize float64

	// 🚀 从数据库获取截图任务统计
	var completedTasks []task.ScreenshotTask
	dbpool.ExecuteDBOperationAsyncAndWait("screenshot_task_stats", func(db *gorm.DB) error {
		db.Model(&task.ScreenshotTask{}).Count(&totalScreenshots)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "running").Count(&activeScreenshots)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "completed").Count(&completedScreenshots)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "failed").Count(&failedScreenshots)

		// 计算平均文件大小
		db.Where("status = ? AND file_size > 0", "completed").Find(&completedTasks)
		return nil
	})

	if len(completedTasks) > 0 {
		var totalSize int64
		for _, t := range completedTasks {
			totalSize += t.FileSize
		}
		avgSize = float64(totalSize) / float64(len(completedTasks)) / (1024 * 1024) // MB
	}

	return gin.H{
		"total_screenshots":     totalScreenshots,
		"active_screenshots":    activeScreenshots,
		"completed_screenshots": completedScreenshots,
		"failed_screenshots":    failedScreenshots,
		"average_size_mb":       avgSize,
	}
}

// 辅助函数 - 获取进程任务统计
func getProcessTaskStats() gin.H {
	var totalProcessTasks, activeProcessTasks, completedProcessTasks, failedProcessTasks int64

	// 🚀 从数据库获取进程任务统计
	dbpool.ExecuteDBOperationAsyncAndWait("process_task_stats", func(db *gorm.DB) error {
		db.Model(&task.ProcessTask{}).Count(&totalProcessTasks)
		db.Model(&task.ProcessTask{}).Where("status = ?", "running").Count(&activeProcessTasks)
		db.Model(&task.ProcessTask{}).Where("status = ?", "completed").Count(&completedProcessTasks)
		db.Model(&task.ProcessTask{}).Where("status = ?", "failed").Count(&failedProcessTasks)
		return nil
	})

	return gin.H{
		"total_process_tasks":     totalProcessTasks,
		"active_process_tasks":    activeProcessTasks,
		"completed_process_tasks": completedProcessTasks,
		"failed_process_tasks":    failedProcessTasks,
	}
}

// 辅助函数 - 获取网络任务统计
func getNetworkTaskStats() gin.H {
	var totalNetworkTasks, activeNetworkTasks, completedNetworkTasks, failedNetworkTasks int64

	// 🚀 从数据库获取网络任务统计
	dbpool.ExecuteDBOperationAsyncAndWait("network_task_stats", func(db *gorm.DB) error {
		db.Model(&task.NetworkTask{}).Count(&totalNetworkTasks)
		db.Model(&task.NetworkTask{}).Where("status = ?", "running").Count(&activeNetworkTasks)
		db.Model(&task.NetworkTask{}).Where("status = ?", "completed").Count(&completedNetworkTasks)
		db.Model(&task.NetworkTask{}).Where("status = ?", "failed").Count(&failedNetworkTasks)
		return nil
	})

	return gin.H{
		"total_network_tasks":     totalNetworkTasks,
		"active_network_tasks":    activeNetworkTasks,
		"completed_network_tasks": completedNetworkTasks,
		"failed_network_tasks":    failedNetworkTasks,
	}
}

// 辅助函数 - 获取总任务执行数
func getTotalTasksExecuted() int64 {
	var total int64
	var fileTransferCount, screenshotCount, processCount, networkCount int64

	// 🚀 使用数据库连接池进行任务统计
	dbpool.ExecuteDBOperationAsyncAndWait("task_counts_1", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Count(&fileTransferCount)
		db.Model(&task.ScreenshotTask{}).Count(&screenshotCount)
		db.Model(&task.ProcessTask{}).Count(&processCount)
		db.Model(&task.NetworkTask{}).Count(&networkCount)
		return nil
	})

	total = fileTransferCount + screenshotCount + processCount + networkCount
	return total
}

// 辅助函数 - 获取按类型分组的任务数
func getTasksByType() gin.H {
	var fileTransferCount, screenshotCount, processCount, networkCount int64

	// 🚀 使用数据库连接池进行任务统计
	dbpool.ExecuteDBOperationAsyncAndWait("task_counts_2", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Count(&fileTransferCount)
		db.Model(&task.ScreenshotTask{}).Count(&screenshotCount)
		db.Model(&task.ProcessTask{}).Count(&processCount)
		db.Model(&task.NetworkTask{}).Count(&networkCount)
		return nil
	})

	return gin.H{
		"file_transfer": fileTransferCount,
		"screenshot":    screenshotCount,
		"process":       processCount,
		"network":       networkCount,
		"other":         0,
	}
}

// 辅助函数 - 获取平均执行时间
func getAverageExecutionTime() float64 {
	var totalDuration float64
	var taskCount int

	// 计算文件传输任务的平均执行时间
	var fileTransferTasks []task.FileTransferTask
	// 🚀 使用数据库连接池查询已完成的文件传输任务
	dbpool.ExecuteDBOperationAsyncAndWait("completed_file_tasks", func(db *gorm.DB) error {
		return db.Where("status = ? AND completed_at IS NOT NULL AND started_at IS NOT NULL", "completed").
			Find(&fileTransferTasks).Error
	})

	for _, t := range fileTransferTasks {
		if t.CompletedAt != nil && t.StartedAt != nil {
			duration := t.CompletedAt.Sub(*t.StartedAt).Seconds()
			totalDuration += duration
			taskCount++
		}
	}

	// 计算截图任务的平均执行时间
	var screenshotTasks []task.ScreenshotTask
	// 🚀 使用数据库连接池查询已完成的截图任务
	dbpool.ExecuteDBOperationAsyncAndWait("completed_screenshot_tasks", func(db *gorm.DB) error {
		return db.Where("status = ? AND completed_at IS NOT NULL AND started_at IS NOT NULL", "completed").
			Find(&screenshotTasks).Error
	})

	for _, t := range screenshotTasks {
		if t.CompletedAt != nil && t.StartedAt != nil {
			duration := t.CompletedAt.Sub(*t.StartedAt).Seconds()
			totalDuration += duration
			taskCount++
		}
	}

	if taskCount > 0 {
		return totalDuration / float64(taskCount)
	}
	return 0.0
}

// 辅助函数 - 获取任务成功率
func getTaskSuccessRate() float64 {
	totalTasks := getTotalTasksExecuted()
	if totalTasks == 0 {
		return 0.0
	}

	var completedTasks int64
	var fileTransferCompleted, screenshotCompleted, processCompleted, networkCompleted int64

	// 🚀 使用数据库连接池统计已完成任务
	dbpool.ExecuteDBOperationAsyncAndWait("completed_task_counts", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Where("status = ?", "completed").Count(&fileTransferCompleted)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "completed").Count(&screenshotCompleted)
		db.Model(&task.ProcessTask{}).Where("status = ?", "completed").Count(&processCompleted)
		db.Model(&task.NetworkTask{}).Where("status = ?", "completed").Count(&networkCompleted)
		return nil
	})

	completedTasks = fileTransferCompleted + screenshotCompleted + processCompleted + networkCompleted

	return float64(completedTasks) / float64(totalTasks) * 100
}

// 辅助函数 - 获取失败任务数
func getFailedTasksCount() int64 {
	var failedTasks int64
	var fileTransferFailed, screenshotFailed, processFailed, networkFailed int64

	// 🚀 使用数据库连接池统计失败任务
	dbpool.ExecuteDBOperationAsyncAndWait("failed_task_counts", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Where("status = ?", "failed").Count(&fileTransferFailed)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "failed").Count(&screenshotFailed)
		db.Model(&task.ProcessTask{}).Where("status = ?", "failed").Count(&processFailed)
		db.Model(&task.NetworkTask{}).Where("status = ?", "failed").Count(&networkFailed)
		return nil
	})

	failedTasks = fileTransferFailed + screenshotFailed + processFailed + networkFailed
	return failedTasks
}

// 辅助函数 - 获取待处理任务数
func getPendingTasksCount() int64 {
	var pendingTasks int64
	var fileTransferPending, screenshotPending, processPending, networkPending int64

	// 🚀 使用数据库连接池统计待处理任务
	dbpool.ExecuteDBOperationAsyncAndWait("pending_task_counts", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Where("status = ?", "pending").Count(&fileTransferPending)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "pending").Count(&screenshotPending)
		db.Model(&task.ProcessTask{}).Where("status = ?", "pending").Count(&processPending)
		db.Model(&task.NetworkTask{}).Where("status = ?", "pending").Count(&networkPending)
		return nil
	})

	pendingTasks = fileTransferPending + screenshotPending + processPending + networkPending
	return pendingTasks
}

// 辅助函数 - 获取运行中任务数
func getRunningTasksCount() int64 {
	var runningTasks int64
	var fileTransferRunning, screenshotRunning, processRunning, networkRunning int64

	// 🚀 使用数据库连接池统计运行中任务
	dbpool.ExecuteDBOperationAsyncAndWait("running_task_counts", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Where("status = ?", "running").Count(&fileTransferRunning)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "running").Count(&screenshotRunning)
		db.Model(&task.ProcessTask{}).Where("status = ?", "running").Count(&processRunning)
		db.Model(&task.NetworkTask{}).Where("status = ?", "running").Count(&networkRunning)
		return nil
	})

	runningTasks = fileTransferRunning + screenshotRunning + processRunning + networkRunning
	return runningTasks
}

// 辅助函数 - 获取已完成任务数
func getCompletedTasksCount() int64 {
	var completedTasks int64
	var fileTransferCompleted, screenshotCompleted, processCompleted, networkCompleted int64

	// 🚀 使用数据库连接池统计已完成任务（重复统计）
	dbpool.ExecuteDBOperationAsyncAndWait("completed_task_counts_2", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).Where("status = ?", "completed").Count(&fileTransferCompleted)
		db.Model(&task.ScreenshotTask{}).Where("status = ?", "completed").Count(&screenshotCompleted)
		db.Model(&task.ProcessTask{}).Where("status = ?", "completed").Count(&processCompleted)
		db.Model(&task.NetworkTask{}).Where("status = ?", "completed").Count(&networkCompleted)
		return nil
	})

	completedTasks = fileTransferCompleted + screenshotCompleted + processCompleted + networkCompleted
	return completedTasks
}

// 辅助函数 - 获取队列长度
func getQueueLength() int64 {
	// 队列长度 = 待处理任务数 + 运行中任务数
	return getPendingTasksCount() + getRunningTasksCount()
}

// 辅助函数 - 获取处理速率
func getProcessingRate() float64 {
	// 计算最近1小时内完成的任务数，得出任务/秒的处理速率
	oneHourAgo := time.Now().Add(-time.Hour)

	var recentCompletedTasks int64
	var fileTransferRecent, screenshotRecent, processRecent, networkRecent int64

	// 🚀 使用数据库连接池统计最近一小时完成的任务
	dbpool.ExecuteDBOperationAsyncAndWait("recent_completed_tasks", func(db *gorm.DB) error {
		db.Model(&task.FileTransferTask{}).
			Where("status = ? AND completed_at > ?", "completed", oneHourAgo).
			Count(&fileTransferRecent)
		db.Model(&task.ScreenshotTask{}).
			Where("status = ? AND completed_at > ?", "completed", oneHourAgo).
			Count(&screenshotRecent)
		db.Model(&task.ProcessTask{}).
			Where("status = ? AND completed_at > ?", "completed", oneHourAgo).
			Count(&processRecent)
		db.Model(&task.NetworkTask{}).
			Where("status = ? AND completed_at > ?", "completed", oneHourAgo).
			Count(&networkRecent)
		return nil
	})

	recentCompletedTasks = fileTransferRecent + screenshotRecent + processRecent + networkRecent

	// 转换为任务/秒
	return float64(recentCompletedTasks) / 3600.0
}

// 辅助函数 - 获取客户端内存池统计
func getClientMemoryPoolStats() gin.H {
	// 这里可以扩展获取客户端的内存池统计
	return gin.H{
		"windows_clients": gin.H{
			"total_memory_usage_mb": 0.0,
			"pool_efficiency":       0.0,
		},
		"linux_clients": gin.H{
			"total_memory_usage_mb": 0.0,
			"pool_efficiency":       0.0,
		},
		"macos_clients": gin.H{
			"total_memory_usage_mb": 0.0,
			"pool_efficiency":       0.0,
		},
	}
}

// GetGoroutineStats 获取Goroutine统计信息
func (p *PerformanceApi) GetGoroutineStats(ctx *gin.Context) {
	// 🚀 使用工作池替代直接goroutine，确保被监控
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	// 🚀 优化：提交到处理池进行CPU密集型统计计算
	task := workerpool.NewProcessingTask("get_goroutine_stats", func() error {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取Goroutine统计时发生panic: %v", r)
			}
		}()

		stats := workerpool.GetGoroutineStats()
		responseData := gin.H{
			"goroutine_stats": stats,
			"timestamp":       time.Now(),
			"uptime":          time.Since(global.StartTime),
		}
		done <- responseData
		return nil
	})

	if err := workerpool.SubmitProcessingTask(task); err != nil {
		global.LOG.Error("提交Goroutine统计任务失败", zap.Error(err))
		response.ErrorWithMessage("提交统计任务失败", ctx)
		return
	}

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case err := <-errChan:
		global.LOG.Error("获取Goroutine统计失败", zap.Error(err))
		response.ErrorWithMessage("获取Goroutine统计失败: "+err.Error(), ctx)
	case <-time.After(3 * time.Second):
		global.LOG.Warn("获取Goroutine统计超时")
		response.ErrorWithMessage("获取Goroutine统计超时", ctx)
	}
}

// GetWorkerPoolStats 获取工作池统计信息
func (p *PerformanceApi) GetWorkerPoolStats(ctx *gin.Context) {
	// 🚀 使用工作池替代直接goroutine
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	// 🚀 优化：使用处理池进行CPU密集型统计计算
	task := workerpool.NewProcessingTask("get_worker_pool_stats", func() error {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取工作池统计时发生panic: %v", r)
			}
		}()

		allStats := workerpool.GetAllPoolStatsCompatible()
		summary := calculatePoolSummaryFromCompatible(allStats)

		responseData := gin.H{
			"pool_stats": allStats,
			"summary":    summary,
			"timestamp":  time.Now(),
		}
		done <- responseData
		return nil
	})

	if err := workerpool.SubmitProcessingTask(task); err != nil {
		global.LOG.Error("提交工作池统计任务失败", zap.Error(err))
		response.ErrorWithMessage("提交统计任务失败", ctx)
		return
	}

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case err := <-errChan:
		global.LOG.Error("获取工作池统计失败", zap.Error(err))
		response.ErrorWithMessage("获取工作池统计失败: "+err.Error(), ctx)
	case <-time.After(3 * time.Second):
		global.LOG.Warn("获取工作池统计超时")
		response.ErrorWithMessage("获取工作池统计超时", ctx)
	}
}

// GetLeakDetectorStats 获取泄漏检测器统计信息
func (p *PerformanceApi) GetLeakDetectorStats(ctx *gin.Context) {
	if workerpool.GlobalLeakDetector == nil {
		response.ErrorWithMessage("泄漏检测器未初始化", ctx)
		return
	}

	stats := workerpool.GlobalLeakDetector.GetStats()
	history := workerpool.GlobalLeakDetector.GetHistory()

	responseData := gin.H{
		"stats":     stats,
		"history":   history,
		"timestamp": time.Now(),
	}

	response.OkWithData(responseData, ctx)
}

// GetSystemStats 获取系统统计信息
func (p *PerformanceApi) GetSystemStats(ctx *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	responseData := gin.H{
		"num_cpu":       runtime.NumCPU(),
		"num_goroutine": runtime.NumGoroutine(),
		"mem_stats": gin.H{
			"alloc":          m.Alloc,
			"total_alloc":    m.TotalAlloc,
			"sys":            m.Sys,
			"lookups":        m.Lookups,
			"mallocs":        m.Mallocs,
			"frees":          m.Frees,
			"heap_alloc":     m.HeapAlloc,
			"heap_sys":       m.HeapSys,
			"heap_idle":      m.HeapIdle,
			"heap_inuse":     m.HeapInuse,
			"heap_released":  m.HeapReleased,
			"heap_objects":   m.HeapObjects,
			"stack_inuse":    m.StackInuse,
			"stack_sys":      m.StackSys,
			"mspan_inuse":    m.MSpanInuse,
			"mspan_sys":      m.MSpanSys,
			"mcache_inuse":   m.MCacheInuse,
			"mcache_sys":     m.MCacheSys,
			"buck_hash_sys":  m.BuckHashSys,
			"gc_sys":         m.GCSys,
			"other_sys":      m.OtherSys,
			"next_gc":        m.NextGC,
			"last_gc":        time.Unix(0, int64(m.LastGC)),
			"pause_total_ns": m.PauseTotalNs,
			"num_gc":         m.NumGC,
			"num_forced_gc":  m.NumForcedGC,
			"gc_cpu_fraction": m.GCCPUFraction,
		},
		"timestamp": time.Now(),
		"uptime":    time.Since(global.StartTime),
	}

	response.OkWithData(responseData, ctx)
}

// GetPerformanceOverview 获取性能概览
func (p *PerformanceApi) GetPerformanceOverview(ctx *gin.Context) {
	// 🚨 修复阻塞问题：添加超时和错误处理
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	// 🚀 优化：使用处理池进行CPU密集型性能数据计算
	task := workerpool.NewProcessingTask("get_performance_overview", func() error {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取性能统计时发生panic: %v", r)
			}
		}()

		// 获取各种统计信息
		goroutineStats := workerpool.GetGoroutineStats()
		poolStats := workerpool.GetAllPoolStatsCompatible()

		var leakStats *workerpool.LeakStats
		if workerpool.GlobalLeakDetector != nil {
			leakStats = workerpool.GlobalLeakDetector.GetStats()
		}

		var m runtime.MemStats
		runtime.ReadMemStats(&m)

		responseData := gin.H{
			"timestamp":       time.Now(),
			"uptime":          time.Since(global.StartTime),
			"num_cpu":         runtime.NumCPU(),
			"num_goroutine":   runtime.NumGoroutine(),
			"memory_usage":    m.Alloc,
			"total_memory":    m.TotalAlloc,
			"goroutine_stats": goroutineStats,
			"pool_summary":    calculatePoolSummaryFromCompatible(poolStats),
			"leak_stats":      leakStats,
			"health_status":   calculateHealthStatusFromCompatible(goroutineStats, poolStats, leakStats),
		}

		done <- responseData
		return nil
	})

	if err := workerpool.SubmitProcessingTask(task); err != nil {
		global.LOG.Error("提交性能概览任务失败", zap.Error(err))
		response.ErrorWithMessage("提交统计任务失败", ctx)
		return
	}

	// 等待结果或超时
	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case err := <-errChan:
		global.LOG.Error("获取性能概览失败", zap.Error(err))
		response.ErrorWithMessage("获取性能概览失败: "+err.Error(), ctx)
	case <-time.After(5 * time.Second): // 5秒超时
		global.LOG.Warn("获取性能概览超时")
		response.ErrorWithMessage("获取性能概览超时", ctx)
	}
}

// calculatePoolSummaryFromCompatible 从兼容格式计算工作池汇总信息
func calculatePoolSummaryFromCompatible(poolStats map[string]interface{}) gin.H {
	summary := gin.H{
		"total_pools":     0,
		"total_workers":   int64(0),
		"total_tasks":     int64(0),
		"total_completed": int64(0),
		"total_failed":    int64(0),
		"total_queued":    int64(0),
		"total_active":    int64(0),
		"success_rate":    float64(0),
		"max_queue_usage": float64(0),
	}

	totalPools := 0
	var totalWorkers, totalTasks, totalCompleted, totalFailed, totalQueued, totalActive int64
	var maxQueueUsage float64

	for _, statsInterface := range poolStats {
		if stats, ok := statsInterface.(map[string]interface{}); ok {
			totalPools++

			if val, ok := stats["total_workers"].(int32); ok {
				totalWorkers += int64(val)
			}
			if val, ok := stats["total_tasks"].(int64); ok {
				totalTasks += val
			}
			if val, ok := stats["completed_tasks"].(int64); ok {
				totalCompleted += val
			}
			if val, ok := stats["failed_tasks"].(int64); ok {
				totalFailed += val
			}
			if val, ok := stats["queued_tasks"].(int64); ok {
				totalQueued += val
			}
			if val, ok := stats["active_tasks"].(int64); ok {
				totalActive += val
			}
			if val, ok := stats["queue_usage"].(float64); ok && val > maxQueueUsage {
				maxQueueUsage = val
			}
		}
	}

	summary["total_pools"] = totalPools
	summary["total_workers"] = totalWorkers
	summary["total_tasks"] = totalTasks
	summary["total_completed"] = totalCompleted
	summary["total_failed"] = totalFailed
	summary["total_queued"] = totalQueued
	summary["total_active"] = totalActive
	summary["max_queue_usage"] = maxQueueUsage

	if totalTasks > 0 {
		summary["success_rate"] = float64(totalCompleted) / float64(totalTasks)
	}

	return summary
}

// 🗑️ 已删除未使用的calculatePoolSummary函数

// calculateHealthStatusFromCompatible 从兼容格式计算健康状态
func calculateHealthStatusFromCompatible(goroutineStats *workerpool.GoroutineStats,
	poolStats map[string]interface{},
	leakStats *workerpool.LeakStats) string {

	// 检查Goroutine数量
	if goroutineStats.NumGoroutine > 5000 {
		return "危险"
	}

	// 检查内存使用
	if goroutineStats.MemoryUsage > 1024*1024*1024 { // 1GB
		return "警告"
	}

	// 检查工作池队列使用率
	for _, statsInterface := range poolStats {
		if stats, ok := statsInterface.(map[string]interface{}); ok {
			if val, ok := stats["queue_usage"].(float64); ok && val > 0.9 {
				return "警告"
			}
		}
	}

	// 检查泄漏检测
	if leakStats != nil && leakStats.LeaksDetected > 0 {
		return "警告"
	}

	return "健康"
}

// 🗑️ 已删除未使用的calculateHealthStatus函数

// GetDatabaseStats 获取数据库连接池统计信息
func (p *PerformanceApi) GetDatabaseStats(ctx *gin.Context) {
	// 🚀 使用工作池替代直接goroutine
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	task := workerpool.NewDatabaseTask("get_database_stats", func() error {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取数据库统计时发生panic: %v", r)
			}
		}()

		stats := dbpool.GetDBStats()
		responseData := gin.H{
			"database_stats": stats,
			"timestamp":      time.Now(),
		}
		done <- responseData
		return nil
	})

	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库统计任务失败", zap.Error(err))
		response.ErrorWithMessage("提交统计任务失败", ctx)
		return
	}

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case err := <-errChan:
		global.LOG.Error("获取数据库统计失败", zap.Error(err))
		response.ErrorWithMessage("获取数据库统计失败: "+err.Error(), ctx)
	case <-time.After(3 * time.Second):
		global.LOG.Warn("获取数据库统计超时")
		response.ErrorWithMessage("获取数据库统计超时", ctx)
	}
}

// GetDatabaseHealth 获取数据库健康状态
func (p *PerformanceApi) GetDatabaseHealth(ctx *gin.Context) {
	// 🚨 添加超时保护
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取数据库健康状态时发生panic: %v", r)
			}
		}()

		err := dbpool.CheckDBHealth()
		stats := dbpool.GetGlobalDBPoolStatsCompatible()

		responseData := gin.H{
			"health_status":    stats["health_status"],
			"is_healthy":       err == nil,
			"last_check_time":  time.Now(),
			"connection_stats": map[string]interface{}{
				"open_conns":   stats["open_conns"],
				"in_use_conns": stats["in_use_conns"],
				"idle_conns":   stats["idle_conns"],
			},
			"query_stats": map[string]interface{}{
				"total_queries":  stats["total_queries"],
				"success_rate":   stats["success_rate"],
				"slow_queries":   stats["slow_queries"],
			},
		}

		if err != nil {
			responseData["error"] = err.Error()
		}

		done <- responseData
	}()

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case err := <-errChan:
		global.LOG.Error("获取数据库健康状态失败", zap.Error(err))
		response.ErrorWithMessage("获取数据库健康状态失败: "+err.Error(), ctx)
	case <-time.After(5 * time.Second):
		global.LOG.Warn("获取数据库健康状态超时")
		response.ErrorWithMessage("获取数据库健康状态超时", ctx)
	}
}

// GetDatabaseConnectionStats 获取数据库连接统计
func (p *PerformanceApi) GetDatabaseConnectionStats(ctx *gin.Context) {
	// 🚨 添加超时保护
	done := make(chan gin.H, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				global.LOG.Error("获取数据库连接统计时发生panic", zap.Any("panic", r))
			}
		}()

		stats := dbpool.GetGlobalDBPoolStatsCompatible()

		responseData := gin.H{
			"connection_stats": map[string]interface{}{
				"max_open_conns":         stats["max_open_conns"],
				"max_idle_conns":         stats["max_idle_conns"],
				"open_conns":             stats["open_conns"],
				"in_use_conns":           stats["in_use_conns"],
				"idle_conns":             stats["idle_conns"],
				"connection_utilization": stats["connection_utilization"],
				"connections_created":    stats["connections_created"],
				"connections_closed":     stats["connections_closed"],
				"connection_errors":      stats["connection_errors"],
			},
			"timestamp": time.Now(),
		}
		done <- responseData
	}()

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case <-time.After(3 * time.Second):
		global.LOG.Warn("获取数据库连接统计超时")
		response.ErrorWithMessage("获取数据库连接统计超时", ctx)
	}
}

// GetDatabaseQueryStats 获取数据库查询统计
func (p *PerformanceApi) GetDatabaseQueryStats(ctx *gin.Context) {
	// 🚨 添加超时保护
	done := make(chan gin.H, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				global.LOG.Error("获取数据库查询统计时发生panic", zap.Any("panic", r))
			}
		}()

		stats := dbpool.GetGlobalDBPoolStatsCompatible()

		responseData := gin.H{
			"query_stats": map[string]interface{}{
				"total_queries":   stats["total_queries"],
				"success_queries": stats["success_queries"],
				"failed_queries":  stats["failed_queries"],
				"slow_queries":    stats["slow_queries"],
				"success_rate":    stats["success_rate"],
				"avg_query_time":  stats["avg_query_time"],
				"max_query_time":  stats["max_query_time"],
				"min_query_time":  stats["min_query_time"],
				"query_timeouts":  stats["query_timeouts"],
				"retry_attempts":  stats["retry_attempts"],
			},
			"timestamp": time.Now(),
		}
		done <- responseData
	}()

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case <-time.After(3 * time.Second):
		global.LOG.Warn("获取数据库查询统计超时")
		response.ErrorWithMessage("获取数据库查询统计超时", ctx)
	}
}

// GetCacheStats 获取缓存统计信息
func (p *PerformanceApi) GetCacheStats(ctx *gin.Context) {
	// 🚀 使用工作池获取缓存统计
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	task := workerpool.NewGeneralTask("get_cache_stats", func() error {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取缓存统计时发生panic: %v", r)
			}
		}()

		// 获取智能缓存统计
		var smartCacheStats map[string]interface{}
		if manager.GlobalSmartCache != nil {
			cacheStats := manager.GlobalSmartCache.GetStats()
			smartCacheStats = map[string]interface{}{
				"total_items":     cacheStats.TotalItems,
				"task_items":      cacheStats.TaskItems,
				"command_items":   cacheStats.CommandItems,
				"stream_items":    cacheStats.StreamItems,
				"general_items":   cacheStats.GeneralItems,
				"hit_count":       cacheStats.HitCount,
				"miss_count":      cacheStats.MissCount,
				"evict_count":     cacheStats.EvictCount,
				"expire_count":    cacheStats.ExpireCount,
				"memory_usage":    cacheStats.MemoryUsage,
				"max_memory_usage": cacheStats.MaxMemoryUsage,
				"start_time":      time.Unix(0, cacheStats.StartTime),
				"last_clean_time": time.Unix(0, cacheStats.LastCleanTime),
				"hit_rate":        float64(cacheStats.HitCount) / float64(cacheStats.HitCount + cacheStats.MissCount),
			}
		} else {
			smartCacheStats = map[string]interface{}{
				"status": "未初始化",
			}
		}

		// 获取ResponseManager统计
		responseManagerStats := manager.ResponseMgr.GetCompatibleStats()

		responseData := gin.H{
			"smart_cache_stats":      smartCacheStats,
			"response_manager_stats": responseManagerStats,
			"timestamp":              time.Now(),
		}
		done <- responseData
		return nil
	})

	if err := workerpool.SubmitGeneralTask(task); err != nil {
		global.LOG.Error("提交缓存统计任务失败", zap.Error(err))
		response.ErrorWithMessage("提交统计任务失败", ctx)
		return
	}

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case err := <-errChan:
		global.LOG.Error("获取缓存统计失败", zap.Error(err))
		response.ErrorWithMessage("获取缓存统计失败: "+err.Error(), ctx)
	case <-time.After(3 * time.Second):
		global.LOG.Warn("获取缓存统计超时")
		response.ErrorWithMessage("获取缓存统计超时", ctx)
	}
}

// GetCacheHealth 获取缓存健康状态
func (p *PerformanceApi) GetCacheHealth(ctx *gin.Context) {
	// 🚀 使用工作池获取缓存健康状态
	done := make(chan gin.H, 1)
	errChan := make(chan error, 1)

	task := workerpool.NewGeneralTask("get_cache_health", func() error {
		defer func() {
			if r := recover(); r != nil {
				errChan <- fmt.Errorf("获取缓存健康状态时发生panic: %v", r)
			}
		}()

		healthStatus := "健康"
		var issues []string

		// 检查智能缓存
		if manager.GlobalSmartCache != nil {
			cacheStats := manager.GlobalSmartCache.GetStats()

			// 检查命中率
			if cacheStats.HitCount + cacheStats.MissCount > 100 {
				hitRate := float64(cacheStats.HitCount) / float64(cacheStats.HitCount + cacheStats.MissCount)
				if hitRate < 0.5 {
					healthStatus = "警告"
					issues = append(issues, "缓存命中率过低")
				}
			}

			// 检查内存使用
			if cacheStats.MemoryUsage > 80*1024*1024 { // 80MB
				healthStatus = "警告"
				issues = append(issues, "缓存内存使用过高")
			}

			// 检查淘汰率
			if cacheStats.EvictCount > cacheStats.TotalItems/2 {
				healthStatus = "警告"
				issues = append(issues, "缓存淘汰率过高")
			}
		} else {
			healthStatus = "错误"
			issues = append(issues, "智能缓存未初始化")
		}

		// 检查ResponseManager
		rmStats := manager.ResponseMgr.GetStats()
		if rmStats.WaitTimeouts > 10 {
			healthStatus = "警告"
			issues = append(issues, "响应等待超时过多")
		}

		responseData := gin.H{
			"health_status": healthStatus,
			"issues":        issues,
			"timestamp":     time.Now(),
		}
		done <- responseData
		return nil
	})

	if err := workerpool.SubmitGeneralTask(task); err != nil {
		global.LOG.Error("提交缓存健康检查任务失败", zap.Error(err))
		response.ErrorWithMessage("提交健康检查任务失败", ctx)
		return
	}

	select {
	case responseData := <-done:
		response.OkWithData(responseData, ctx)
	case err := <-errChan:
		global.LOG.Error("获取缓存健康状态失败", zap.Error(err))
		response.ErrorWithMessage("获取缓存健康状态失败: "+err.Error(), ctx)
	case <-time.After(3 * time.Second):
		global.LOG.Warn("获取缓存健康状态超时")
		response.ErrorWithMessage("获取缓存健康状态超时", ctx)
	}
}


