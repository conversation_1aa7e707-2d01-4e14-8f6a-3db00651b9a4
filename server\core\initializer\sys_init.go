package initializer

import (
	"fmt"
	"sync"
	"time"
	"server/core/manager/events"
	"server/core/initializer/internal"
	"server/core/listener/pipe"
	"server/core/listener/tcp"
	"server/core/manager"
	"server/core/manager/workerpool"
	"server/global"
	"server/service/c2"
	"server/utils"

	"go.uber.org/zap"
)

func InitSystem() {
	// 记录服务器启动时间
	global.StartTime = time.Now()

	// 🚀 阶段1: 基础设施初始化（必须串行）
	initBasicInfrastructure()

	// 🚀 阶段2: 数据层初始化（必须串行）
	initDataLayer()

	// 🚀 阶段3: 核心管理器初始化（可部分并行）
	initCoreManagers()

	// 🚀 阶段4: 业务管理器初始化（可并行）
	initBusinessManagers()

	// 🚀 阶段5: 监听器初始化（可并行）
	initListeners()

	global.LOG.Info("🎉 系统初始化完成",
		zap.Duration("总耗时", time.Since(global.StartTime)))
}

// initBasicInfrastructure 初始化基础设施（必须串行）
func initBasicInfrastructure() {
	startTime := time.Now()

	global.VP = initViper()
	global.LOG = initZap()
	zap.ReplaceGlobals(global.LOG)

	// 自动配置辅助，检测并设置磁盘列表和JWT签名密钥
	utils.AutoConfigHelper()

	global.LOG.Info("✅ 基础设施初始化完成",
		zap.Duration("耗时", time.Since(startTime)))
}

// initDataLayer 初始化数据层（必须串行）
func initDataLayer() {
	startTime := time.Now()

	global.DB = initDB()
	if global.DB != nil {
		RegistTable()
	}
	OtherInit()
	internal.SysUserInit()

	global.LOG.Info("✅ 数据层初始化完成",
		zap.Duration("耗时", time.Since(startTime)))
}

// initCoreManagers 初始化核心管理器（可部分并行）
func initCoreManagers() {
	startTime := time.Now()

	// 智能缓存系统（独立）
	manager.InitGlobalSmartCache()

	// 工作池管理器（独立）
	workerpool.GlobalPoolManager.InitDefaultPools()

	// Goroutine泄漏检测器（依赖工作池）
	workerpool.InitGlobalLeakDetector()

	global.LOG.Info("✅ 核心管理器初始化完成",
		zap.Duration("耗时", time.Since(startTime)))
}

// initBusinessManagers 初始化业务管理器（可并行）
func initBusinessManagers() {
	startTime := time.Now()

	// 使用WaitGroup并行初始化独立的管理器
	var wg sync.WaitGroup
	var initErrors []error
	var errorMutex sync.Mutex

	// 代理管理器（依赖数据库）
	wg.Add(1)
	go func() {
		defer wg.Done()
		manager.InitProxyManagers()
		global.LOG.Debug("代理管理器初始化完成")
	}()

	// SSE管理器（独立）
	wg.Add(1)
	go func() {
		defer wg.Done()
		manager.InitSSEManager()
		global.LOG.Debug("SSE管理器初始化完成")
	}()

	// 客户端事件管理器（独立）
	wg.Add(1)
	go func() {
		defer wg.Done()
		events.InitClientEventManager()
		global.LOG.Debug("客户端事件管理器初始化完成")
	}()

	// 等待所有管理器初始化完成
	wg.Wait()

	// 客户端事件处理器（依赖事件管理器）
	clientService := &c2.ClientService{}
	clientService.InitClientEventHandlers()

	// 心跳配置（依赖数据库）
	heartbeatConfigService := &c2.HeartbeatConfigService{}
	if err := heartbeatConfigService.InitDefaultGlobalConfig(); err != nil {
		errorMutex.Lock()
		initErrors = append(initErrors, fmt.Errorf("初始化默认心跳配置失败: %w", err))
		errorMutex.Unlock()
		global.LOG.Error("初始化默认心跳配置失败", zap.Error(err))
	} else {
		global.LOG.Info("初始化默认心跳配置成功")
	}

	// 检查是否有初始化错误
	if len(initErrors) > 0 {
		global.LOG.Warn("业务管理器初始化过程中出现错误", zap.Int("错误数量", len(initErrors)))
	}

	global.LOG.Info("✅ 业务管理器初始化完成",
		zap.Duration("耗时", time.Since(startTime)))
}

// initListeners 初始化监听器（可并行）
func initListeners() {
	startTime := time.Now()

	// 使用WaitGroup并行初始化监听器
	var wg sync.WaitGroup

	// pipe监听器
	wg.Add(1)
	go func() {
		defer wg.Done()
		pipe.InitPipeListeners()
		global.LOG.Debug("Pipe监听器初始化完成")
	}()

	// TCP监听器
	wg.Add(1)
	go func() {
		defer wg.Done()
		tcp.InitTCPListeners()
		global.LOG.Debug("TCP监听器初始化完成")
	}()

	// 等待所有监听器初始化完成
	wg.Wait()

	global.LOG.Info("✅ 监听器初始化完成",
		zap.Duration("耗时", time.Since(startTime)))
}
