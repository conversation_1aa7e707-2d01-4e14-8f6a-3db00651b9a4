package initializer

import (
	"context"
	"fmt"
	"sync"
	"time"
	"server/core/manager/events"
	"server/core/initializer/internal"
	"server/core/listener/pipe"
	"server/core/listener/tcp"
	"server/core/manager"
	"server/core/manager/workerpool"
	"server/global"
	"server/service/c2"
	"server/utils"

	"go.uber.org/zap"
)

// InitComponent 初始化组件的结构
type InitComponent struct {
	Name         string
	Dependencies []string
	InitFunc     func() error
	Duration     time.Duration
}

// ParallelInitManager 并行初始化管理器
type ParallelInitManager struct {
	components map[string]*InitComponent
	results    map[string]error
	completed  map[string]bool
	mu         sync.RWMutex
	wg         sync.WaitGroup
	ctx        context.Context
	cancel     context.CancelFunc
}

// NewParallelInitManager 创建并行初始化管理器
func NewParallelInitManager() *ParallelInitManager {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	return &ParallelInitManager{
		components: make(map[string]*InitComponent),
		results:    make(map[string]error),
		completed:  make(map[string]bool),
		ctx:        ctx,
		cancel:     cancel,
	}
}

// Register 注册初始化组件
func (pim *ParallelInitManager) Register(name string, deps []string, initFunc func() error) {
	pim.components[name] = &InitComponent{
		Name:         name,
		Dependencies: deps,
		InitFunc:     initFunc,
	}
}

// canExecute 检查组件是否可以执行（所有依赖都已完成）
func (pim *ParallelInitManager) canExecute(componentName string) bool {
	pim.mu.RLock()
	defer pim.mu.RUnlock()

	component := pim.components[componentName]
	for _, dep := range component.Dependencies {
		if !pim.completed[dep] {
			return false
		}
	}
	return true
}

// executeComponent 执行单个组件的初始化
func (pim *ParallelInitManager) executeComponent(componentName string) {
	defer pim.wg.Done()

	component := pim.components[componentName]
	startTime := time.Now()

	// 执行初始化函数
	err := component.InitFunc()
	duration := time.Since(startTime)

	// 记录结果
	pim.mu.Lock()
	pim.results[componentName] = err
	pim.completed[componentName] = true
	component.Duration = duration
	pim.mu.Unlock()

	if err != nil {
		global.LOG.Error("组件初始化失败",
			zap.String("component", componentName),
			zap.Duration("duration", duration),
			zap.Error(err))
	} else {
		global.LOG.Info("组件初始化成功",
			zap.String("component", componentName),
			zap.Duration("duration", duration))
	}
}

// Execute 执行所有组件的并行初始化
func (pim *ParallelInitManager) Execute() error {
	defer pim.cancel()

	totalComponents := len(pim.components)
	executed := make(map[string]bool)

	for len(executed) < totalComponents {
		// 检查是否有可以执行的组件
		readyComponents := make([]string, 0)

		for name := range pim.components {
			if !executed[name] && pim.canExecute(name) {
				readyComponents = append(readyComponents, name)
			}
		}

		if len(readyComponents) == 0 {
			// 检查是否有循环依赖或未满足的依赖
			remaining := make([]string, 0)
			for name := range pim.components {
				if !executed[name] {
					remaining = append(remaining, name)
				}
			}
			return fmt.Errorf("无法继续初始化，可能存在循环依赖或未满足的依赖: %v", remaining)
		}

		// 并行执行所有准备好的组件
		pim.wg.Add(len(readyComponents))
		for _, componentName := range readyComponents {
			executed[componentName] = true
			go pim.executeComponent(componentName)
		}

		// 等待这一批组件完成
		pim.wg.Wait()

		// 检查是否有错误
		pim.mu.RLock()
		for _, componentName := range readyComponents {
			if pim.results[componentName] != nil {
				pim.mu.RUnlock()
				return fmt.Errorf("组件 %s 初始化失败: %v", componentName, pim.results[componentName])
			}
		}
		pim.mu.RUnlock()
	}

	return nil
}

// GetStats 获取初始化统计信息
func (pim *ParallelInitManager) GetStats() map[string]time.Duration {
	pim.mu.RLock()
	defer pim.mu.RUnlock()

	stats := make(map[string]time.Duration)
	for name, component := range pim.components {
		stats[name] = component.Duration
	}
	return stats
}

func InitSystem() {
	// 记录服务器启动时间
	startTime := time.Now()
	global.StartTime = startTime

	// 🚀 使用并行初始化管理器
	initManager := NewParallelInitManager()

	// 注册所有初始化组件及其依赖关系
	registerInitComponents(initManager)

	// 执行并行初始化
	if err := initManager.Execute(); err != nil {
		panic(fmt.Sprintf("系统初始化失败: %v", err))
	}

	// 输出初始化统计信息
	stats := initManager.GetStats()
	totalDuration := time.Since(startTime)

	global.LOG.Info("🚀 系统初始化完成",
		zap.Duration("总耗时", totalDuration),
		zap.Int("组件数量", len(stats)))

	// 输出各组件初始化时间
	for componentName, duration := range stats {
		global.LOG.Debug("组件初始化耗时",
			zap.String("组件", componentName),
			zap.Duration("耗时", duration))
	}
}

// registerInitComponents 注册所有初始化组件
func registerInitComponents(initManager *ParallelInitManager) {
	// 第一层：基础配置组件（无依赖，可并行）
	initManager.Register("viper", []string{}, func() error {
		global.VP = initViper()
		return nil
	})

	initManager.Register("zap", []string{}, func() error {
		global.LOG = initZap()
		zap.ReplaceGlobals(global.LOG)
		return nil
	})

	// 第二层：依赖基础配置的组件
	initManager.Register("config_helper", []string{"viper", "zap"}, func() error {
		utils.AutoConfigHelper()
		return nil
	})

	initManager.Register("database", []string{"zap"}, func() error {
		global.DB = initDB()
		return nil
	})

	// 第三层：依赖数据库的组件
	initManager.Register("tables", []string{"database"}, func() error {
		if global.DB != nil {
			RegistTable()
		}
		return nil
	})

	initManager.Register("other_init", []string{"database", "config_helper"}, func() error {
		OtherInit()
		return nil
	})

	initManager.Register("sys_user", []string{"tables"}, func() error {
		internal.SysUserInit()
		return nil
	})

	// 第四层：核心管理器组件（可并行）
	initManager.Register("smart_cache", []string{"zap"}, func() error {
		manager.InitGlobalSmartCache()
		return nil
	})

	initManager.Register("workerpool", []string{"zap"}, func() error {
		workerpool.GlobalPoolManager.InitDefaultPools()
		return nil
	})

	initManager.Register("leak_detector", []string{"workerpool"}, func() error {
		workerpool.InitGlobalLeakDetector()
		return nil
	})

	// 第五层：业务管理器组件
	initManager.Register("proxy_manager", []string{"database", "workerpool"}, func() error {
		manager.InitProxyManagers()
		return nil
	})

	initManager.Register("sse_manager", []string{"zap"}, func() error {
		manager.InitSSEManager()
		return nil
	})

	initManager.Register("event_manager", []string{"zap"}, func() error {
		events.InitClientEventManager()
		return nil
	})

	// 第六层：服务层组件
	initManager.Register("client_service", []string{"event_manager", "database"}, func() error {
		clientService := &c2.ClientService{}
		clientService.InitClientEventHandlers()
		return nil
	})

	initManager.Register("heartbeat_config", []string{"database"}, func() error {
		heartbeatConfigService := &c2.HeartbeatConfigService{}
		if err := heartbeatConfigService.InitDefaultGlobalConfig(); err != nil {
			return fmt.Errorf("初始化默认心跳配置失败: %w", err)
		}
		return nil
	})

	// 第七层：监听器组件（最后初始化，可并行）
	initManager.Register("pipe_listeners", []string{"database", "workerpool", "proxy_manager"}, func() error {
		pipe.InitPipeListeners()
		return nil
	})

	initManager.Register("tcp_listeners", []string{"database", "workerpool", "proxy_manager", "heartbeat_config"}, func() error {
		tcp.InitTCPListeners()
		return nil
	})
}
