package tcp

import (
	"server/core/manager/dbpool"
	"server/core/manager"
	"server/global"
	"server/model/basic"
	"server/model/response/proxy"
	"server/model/task"
	"server/model/tlv"
	"server/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func (l *TCPListener) handleProxyPacket(remoteAddr string, packet *tlv.Packet) error {
	switch packet.Header.Code {
	case tlv.CheckPort:
		return l.handleProxyOperationResponse(remoteAddr, packet, "check_port")
	case tlv.ProxyStart:
		return l.handleProxyOperationResponse(remoteAddr, packet, "start_proxy")
	case tlv.ProxyStop:
		return l.handleProxyOperationResponse(remoteAddr, packet, "stop_proxy")
	case tlv.ProxyDelete:
		return l.handleProxyOperationResponse(remoteAddr, packet, "delete_proxy")
	default:
		global.LOG.Warn("未知的代理操作类型", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))
		return nil
	}
}

func (l *TCPListener) handleProxyOperationResponse(remoteAddr string, packet *tlv.Packet, operation string) error {
	switch operation {
	case "check_port":
		var resp *proxy.CheckPortResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &resp); err != nil {
			global.LOG.Error("反序列化端口检测响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}
		go HandleCheckClientPort(resp)
		if resp.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(resp.TaskID, "check_port", resp, resp.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}
		return nil
	case "start_proxy":
		var resp *proxy.ProxyStartResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &resp); err != nil {
			global.LOG.Error("反序列化开始代理响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}
		go HandleProxyStartResponse(resp)
		if resp.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(resp.TaskID, "start_process", resp, resp.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}
		return nil
	case "stop_proxy":
		var resp *proxy.ProxyStopResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &resp); err != nil {
			global.LOG.Error("反序列化停止代理响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}
		go HandleProxyStopResponse(resp)
		if resp.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(resp.TaskID, "stop_proxy", resp, resp.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}
		return nil
	case "delete_proxy":
		var resp *proxy.ProxyDeleteResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &resp); err != nil {
			global.LOG.Error("反序列化删除代理响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}
		go HandleProxyDeleteResponse(resp)
		if resp.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(resp.TaskID, "delete_proxy", resp, resp.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}
		return nil
	default:
		global.LOG.Warn("未知的进程操作类型", zap.String("remoteAddr", remoteAddr), zap.String("operation", operation))
	}

	return nil
}

func HandleCheckClientPort(resp *proxy.CheckPortResponse) {
	pm := manager.GetGlobalPortManager()
	pm.Lock()
	defer pm.Unlock()
	var CheckTask task.ProxyTask
	// 🚀 使用数据库连接池查询任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_task_query", func(db *gorm.DB) error {
		return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).First(&CheckTask).Error
	}); err != nil {
		global.LOG.Error("获取任务失败", zap.Error(err))
		// 🚀 异步更新任务状态
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_task_fail_update", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "获取任务失败",
			}).Error
		})
		return
	}
	// 端口检查响应处理 - 简化版本
	// 正向代理：客户端自己管理端口，服务端只记录日志
	// 反向代理：服务端管理SOCKS5端口，但不需要per-client跟踪
	if len(resp.UnavailablePorts) != 0 {
		global.LOG.Info("客户端端口不可用",
			zap.Uint("clientID", CheckTask.ClientID),
			zap.Any("unavailablePorts", resp.UnavailablePorts))
	}
	if len(resp.AvailablePorts) != 0 {
		global.LOG.Info("客户端端口可用",
			zap.Uint("clientID", CheckTask.ClientID),
			zap.Any("availablePorts", resp.AvailablePorts))
	}
}

func HandleProxyStartResponse(resp *proxy.ProxyStartResponse) {
	if !resp.Success {
		global.LOG.Warn("Client 启动代理失败", zap.String("proxyID", resp.ProxyID), zap.String("error", resp.Error))
		// 🚀 异步更新任务状态为失败
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_start_fail_update", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  resp.Error,
			}).Error
		})

		// 🚀 更新代理状态为失败
		var startProxy basic.Proxy
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_status_fail_update", func(db *gorm.DB) error {
			if err := db.Where("proxy_id = ?", resp.ProxyID).First(&startProxy).Error; err != nil {
				return err
			}
			startProxy.Status = 2
			return db.Updates(&startProxy).Error
		})
		manager.GetGlobalProxyManager().UpdateProxy(&startProxy)
		return
	}

	// 🚀 更新 Proxy 状态为运行中
	var startProxy basic.Proxy
	err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_start_success_update", func(db *gorm.DB) error {
		if err := db.Where("proxy_id = ?", resp.ProxyID).First(&startProxy).Error; err != nil {
			return err
		}
		startProxy.Status = 1
		return db.Save(&startProxy).Error
	})

	if err != nil {
		global.LOG.Warn("获取或更新代理失败", zap.String("proxyID", resp.ProxyID), zap.Error(err))
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_start_fail_task_update", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "获取或更新代理失败",
			}).Error
		})
		return
	}

	// 启动本地中继服务（正向代理）
	if startProxy.Type == "forward" {
		go func() {
			startProxy.ClientPort = resp.Port
			err := manager.GetGlobalProxyManager().StartProxy(&startProxy)
			if err != nil {
				global.LOG.Error("启动正向代理失败", zap.Error(err))
				// 🚀 异步更新任务状态
				dbpool.ExecuteDBOperationAsyncAndWait("proxy_forward_start_fail", func(db *gorm.DB) error {
					return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
						"status": "failed",
						"error":  "启动正向代理失败",
					}).Error
				})
			}
		}()
	}
	manager.GetGlobalProxyManager().UpdateProxy(&startProxy)
}

func HandleProxyStopResponse(resp *proxy.ProxyStopResponse) {
	if !resp.Success {
		global.LOG.Warn("Client 停止代理失败", zap.String("proxyID", resp.ProxyID), zap.String("error", resp.Error))
		// 🚀 异步更新任务状态
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_stop_fail_task", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "停止代理失败",
			}).Error
		})

		// 🚀 异步更新代理状态
		var stopProxy basic.Proxy
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_stop_fail_status", func(db *gorm.DB) error {
			if err := db.Where("proxy_id = ?", resp.ProxyID).First(&stopProxy).Error; err != nil {
				return err
			}
			stopProxy.Status = 2
			return db.Save(&stopProxy).Error
		})
		manager.GetGlobalProxyManager().UpdateProxy(&stopProxy)
		return
	}

	var stopProxy basic.Proxy
	// 🚀 使用数据库连接池查询代理
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_stop_query", func(db *gorm.DB) error {
		return db.Where("proxy_id = ?", resp.ProxyID).First(&stopProxy).Error
	}); err != nil {
		global.LOG.Warn("获取代理失败", zap.String("proxyID", resp.ProxyID))
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_stop_query_fail", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "获取代理失败",
			}).Error
		})
		return
	}
	stopProxy.Status = 0
	if err := manager.GetGlobalProxyManager().UpdateProxy(&stopProxy); err != nil {
		global.LOG.Error("更新代理状态失败", zap.Error(err))
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_stop_update_fail", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "更新代理状态失败",
			}).Error
		})
		return
	}

}

func HandleProxyDeleteResponse(resp *proxy.ProxyDeleteResponse) {
	if !resp.Success {
		global.LOG.Warn("Client 删除代理失败", zap.String("proxyID", resp.ProxyID), zap.String("error", resp.Error))
		// 🚀 异步更新任务状态
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete_fail_task", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "删除代理失败",
			}).Error
		})

		// 🚀 异步更新代理状态
		var delProxy basic.Proxy
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete_fail_status", func(db *gorm.DB) error {
			if err := db.Where("proxy_id = ?", resp.ProxyID).First(&delProxy).Error; err != nil {
				return err
			}
			delProxy.Status = 2
			return db.Save(&delProxy).Error
		})
		manager.GetGlobalProxyManager().UpdateProxy(&delProxy)
		return
	}

	var delProxy basic.Proxy
	// 🚀 使用数据库连接池查询代理
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete_query", func(db *gorm.DB) error {
		return db.Where("proxy_id = ?", resp.ProxyID).First(&delProxy).Error
	}); err != nil {
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete_query_fail", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "获取代理失败",
			}).Error
		})
		return
	}
	delProxy.Status = 0
	if err := manager.GetGlobalProxyManager().UpdateProxy(&delProxy); err != nil {
		dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete_update_fail", func(db *gorm.DB) error {
			return db.Model(&task.ProxyTask{}).Where("id = ?", resp.TaskID).Updates(map[string]interface{}{
				"status": "failed",
				"error":  "更新代理状态失败",
			}).Error
		})
		return
	}
}
