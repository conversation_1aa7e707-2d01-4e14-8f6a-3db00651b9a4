package dbpool

import (
	"context"
	"fmt"
	"server/core/manager/workerpool"
	"server/global"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DBOperationWrapper 数据库操作包装器
type DBOperationWrapper struct {
	db *gorm.DB
}

// NewDBOperationWrapper 创建数据库操作包装器
func NewDBOperationWrapper(db *gorm.DB) *DBOperationWrapper {
	return &DBOperationWrapper{db: db}
}

// WithMonitoring 使用监控执行数据库操作
func (w *DBOperationWrapper) WithMonitoring(operation string, fn func(*gorm.DB) error) error {
	return ExecuteWithMonitoring(operation, func() error {
		return fn(w.db)
	})
}

// WithRetry 使用重试机制执行数据库操作
func (w *DBOperationWrapper) WithRetry(operation string, fn func(*gorm.DB) error, maxRetries int) error {
	return ExecuteWithRetry(operation, func() error {
		return fn(w.db)
	}, maxRetries, time.Second)
}

// WithWorkerpool 使用工作池执行数据库操作
func (w *DBOperationWrapper) WithWorkerpool(operation string, fn func(*gorm.DB) error) error {
	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		return w.WithMonitoring(operation, fn)
	})

	// 提交到数据库工作池
	return workerpool.SubmitDatabaseTask(task)
}

// WithWorkerPoolAndCallback 使用工作池执行数据库操作，支持回调
func (w *DBOperationWrapper) WithWorkerPoolAndCallback(operation string, fn func(*gorm.DB) error, callback func(error)) {
	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		err := w.WithMonitoring(operation, fn)
		if callback != nil {
			callback(err)
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(err)
		}
	}
}

// 🚀 新增：同步等待版本的工作池方法

// WithWorkerpoolAndWait 使用工作池执行数据库操作并等待完成
func (w *DBOperationWrapper) WithWorkerpoolAndWait(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	resultChan := make(chan error, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("数据库操作发生panic: %v", r)
				global.LOG.Error("数据库操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				select {
				case resultChan <- err:
				default:
				}
			}
		}()

		err := w.WithMonitoring(operation, fn)
		select {
		case resultChan <- err:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		return err
	}

	// 等待结果或超时
	select {
	case err := <-resultChan:
		return err
	case <-time.After(timeout):
		return fmt.Errorf("数据库操作超时: %s (超时时间: %v)", operation, timeout)
	}
}

// WithWorkerPoolCallbackAndWait 使用工作池执行数据库操作，支持回调并等待完成
func (w *DBOperationWrapper) WithWorkerPoolCallbackAndWait(operation string, fn func(*gorm.DB) error, callback func(error), timeout time.Duration) error {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	resultChan := make(chan error, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation, func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("数据库操作发生panic: %v", r)
				global.LOG.Error("数据库操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				if callback != nil {
					callback(err)
				}
				select {
				case resultChan <- err:
				default:
				}
			}
		}()

		err := w.WithMonitoring(operation, fn)
		if callback != nil {
			callback(err)
		}
		select {
		case resultChan <- err:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(err)
		}
		return err
	}

	// 等待结果或超时
	select {
	case err := <-resultChan:
		return err
	case <-time.After(timeout):
		err := fmt.Errorf("数据库操作超时: %s (超时时间: %v)", operation, timeout)
		if callback != nil {
			callback(err)
		}
		return err
	}
}

// Transaction 执行事务操作
func (w *DBOperationWrapper) Transaction(operation string, fn func(*gorm.DB) error) error {
	return ExecuteWithMonitoring(operation+"_transaction", func() error {
		return w.db.Transaction(fn)
	})
}

// TransactionWithWorkerpool 使用工作池执行事务操作
func (w *DBOperationWrapper) TransactionWithWorkerpool(operation string, fn func(*gorm.DB) error) error {
	task := workerpool.NewDatabaseTask(operation+"_transaction", func() error {
		return w.Transaction(operation, fn)
	})

	return workerpool.SubmitDatabaseTask(task)
}

// TransactionWithWorkerpoolAndWait 使用工作池执行事务操作并等待完成
func (w *DBOperationWrapper) TransactionWithWorkerpoolAndWait(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	resultChan := make(chan error, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation+"_transaction", func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("事务操作发生panic: %v", r)
				global.LOG.Error("事务操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				select {
				case resultChan <- err:
				default:
				}
			}
		}()

		err := w.Transaction(operation, fn)
		select {
		case resultChan <- err:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交事务任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		return err
	}

	// 等待结果或超时
	select {
	case err := <-resultChan:
		return err
	case <-time.After(timeout):
		return fmt.Errorf("事务操作超时: %s (超时时间: %v)", operation, timeout)
	}
}

// BatchOperation 批量操作
func (w *DBOperationWrapper) BatchOperation(operation string, batchSize int, items interface{}, fn func(*gorm.DB, interface{}) error) error {
	return ExecuteWithMonitoring(operation+"_batch", func() error {
		return w.db.CreateInBatches(items, batchSize).Error
	})
}

// AsyncQuery 异步查询操作
func (w *DBOperationWrapper) AsyncQuery(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error)) {
	task := workerpool.NewDatabaseTask(operation+"_async_query", func() error {
		result, err := fn(w.db)
		if callback != nil {
			callback(result, err)
		}
		return err
	})

	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交异步查询任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(nil, err)
		}
	}
}

// AsyncQueryAndWait 异步查询操作并等待完成
func (w *DBOperationWrapper) AsyncQueryAndWait(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error), timeout time.Duration) (interface{}, error) {
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建结果通道
	type queryResult struct {
		data interface{}
		err  error
	}
	resultChan := make(chan queryResult, 1)

	// 创建数据库任务
	task := workerpool.NewDatabaseTask(operation+"_async_query", func() error {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("查询操作发生panic: %v", r)
				global.LOG.Error("查询操作panic",
					zap.String("operation", operation),
					zap.Any("panic", r))
				if callback != nil {
					callback(nil, err)
				}
				select {
				case resultChan <- queryResult{nil, err}:
				default:
				}
			}
		}()

		result, err := fn(w.db)
		if callback != nil {
			callback(result, err)
		}
		select {
		case resultChan <- queryResult{result, err}:
		default:
		}
		return err
	})

	// 提交到数据库工作池
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交异步查询任务失败",
			zap.String("operation", operation),
			zap.Error(err))
		if callback != nil {
			callback(nil, err)
		}
		return nil, err
	}

	// 等待结果或超时
	select {
	case result := <-resultChan:
		return result.data, result.err
	case <-time.After(timeout):
		err := fmt.Errorf("查询操作超时: %s (超时时间: %v)", operation, timeout)
		if callback != nil {
			callback(nil, err)
		}
		return nil, err
	}
}

// HealthCheck 数据库健康检查
func (w *DBOperationWrapper) HealthCheck() error {
	return ExecuteWithMonitoring("health_check", func() error {
		sqlDB, err := w.db.DB()
		if err != nil {
			return err
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		return sqlDB.PingContext(ctx)
	})
}

// GetDBWrapper 获取全局数据库操作包装器
func GetDBWrapper() *DBOperationWrapper {
	return NewDBOperationWrapper(global.DB)
}

// 便捷函数

// ExecuteDBOperation 执行数据库操作（带监控）
func ExecuteDBOperation(operation string, fn func(*gorm.DB) error) error {
	// 🚀 记录连接池操作
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithMonitoring(operation, fn)
}

// ExecuteDBOperationWithRetry 执行数据库操作（带重试）
func ExecuteDBOperationWithRetry(operation string, fn func(*gorm.DB) error, maxRetries int) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithRetry(operation, fn, maxRetries)
}

// ExecuteDBOperationAsync 异步执行数据库操作
// ⚠️  警告：此函数是真正的异步操作，不会等待数据库操作完成就返回
// ⚠️  不适用于需要立即获取自动生成ID的场景（如 task.ID）
// ⚠️  如需等待操作完成，请使用 ExecuteDBOperationAsyncAndWait
// 适用场景：不需要立即结果的后台任务、日志记录、统计更新等
func ExecuteDBOperationAsync(operation string, fn func(*gorm.DB) error) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithWorkerpool(operation, fn)
}

// ExecuteDBOperationAsyncWithCallback 异步执行数据库操作（带回调）
// ⚠️  警告：此函数是真正的异步操作，不会等待数据库操作完成就返回
// ⚠️  不适用于需要立即获取自动生成ID的场景（如 task.ID）
// ⚠️  如需等待操作完成，请使用 ExecuteDBOperationAsyncWithCallbackAndWait
// 适用场景：不需要立即结果但需要回调通知的后台任务
func ExecuteDBOperationAsyncWithCallback(operation string, fn func(*gorm.DB) error, callback func(error)) {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	wrapper.WithWorkerPoolAndCallback(operation, fn, callback)
}

// 🚀 新增：同步等待版本的异步函数

// ExecuteDBOperationAsyncAndWait 异步执行数据库操作并等待完成
// ✅ 适用于需要立即获取自动生成ID的场景（如 task.ID）
// ✅ 提供超时控制，默认30秒
// ✅ 完整的错误处理和panic恢复
func ExecuteDBOperationAsyncAndWait(operation string, fn func(*gorm.DB) error) error {
	return ExecuteDBOperationAsyncAndWaitWithTimeout(operation, fn, 30*time.Second)
}

// ExecuteDBOperationAsyncAndWaitWithTimeout 异步执行数据库操作并等待完成（自定义超时）
func ExecuteDBOperationAsyncAndWaitWithTimeout(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithWorkerpoolAndWait(operation, fn, timeout)
}

// ExecuteDBOperationAsyncWithCallbackAndWait 异步执行数据库操作（带回调）并等待完成
// ✅ 适用于需要立即获取自动生成ID且需要回调通知的场景
// ✅ 提供超时控制，默认30秒
func ExecuteDBOperationAsyncWithCallbackAndWait(operation string, fn func(*gorm.DB) error, callback func(error)) error {
	return ExecuteDBOperationAsyncWithCallbackAndWaitWithTimeout(operation, fn, callback, 30*time.Second)
}

// ExecuteDBOperationAsyncWithCallbackAndWaitWithTimeout 异步执行数据库操作（带回调）并等待完成（自定义超时）
func ExecuteDBOperationAsyncWithCallbackAndWaitWithTimeout(operation string, fn func(*gorm.DB) error, callback func(error), timeout time.Duration) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithWorkerPoolCallbackAndWait(operation, fn, callback, timeout)
}

// ExecuteDBTransaction 执行数据库事务
func ExecuteDBTransaction(operation string, fn func(*gorm.DB) error) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.Transaction(operation, fn)
}

// ExecuteDBTransactionAsync 异步执行数据库事务
// ⚠️  警告：此函数是真正的异步操作，不会等待事务完成就返回
// ⚠️  不适用于需要立即获取自动生成ID的场景（如 task.ID）
// ⚠️  如需等待事务完成，请使用 ExecuteDBTransactionAsyncAndWait
func ExecuteDBTransactionAsync(operation string, fn func(*gorm.DB) error) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.TransactionWithWorkerpool(operation, fn)
}

// ExecuteDBTransactionAsyncAndWait 异步执行数据库事务并等待完成
// ✅ 适用于需要立即获取自动生成ID的事务场景
// ✅ 提供超时控制，默认30秒
func ExecuteDBTransactionAsyncAndWait(operation string, fn func(*gorm.DB) error) error {
	return ExecuteDBTransactionAsyncAndWaitWithTimeout(operation, fn, 30*time.Second)
}

// ExecuteDBTransactionAsyncAndWaitWithTimeout 异步执行数据库事务并等待完成（自定义超时）
func ExecuteDBTransactionAsyncAndWaitWithTimeout(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.TransactionWithWorkerpoolAndWait(operation, fn, timeout)
}

// ExecuteDBBatch 执行批量数据库操作
func ExecuteDBBatch(operation string, batchSize int, items interface{}) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.BatchOperation(operation, batchSize, items, nil)
}

// QueryDBAsync 异步查询数据库
// ⚠️  警告：此函数是真正的异步操作，不会等待查询完成就返回
// ⚠️  结果只能通过回调函数获取
// ⚠️  如需等待查询完成并获取结果，请使用 QueryDBAsyncAndWait
func QueryDBAsync(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error)) {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	wrapper.AsyncQuery(operation, fn, callback)
}

// QueryDBAsyncAndWait 异步查询数据库并等待完成
// ✅ 适用于需要立即获取查询结果的场景
// ✅ 提供超时控制，默认30秒
// ✅ 支持可选的回调函数
func QueryDBAsyncAndWait(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error)) (interface{}, error) {
	return QueryDBAsyncAndWaitWithTimeout(operation, fn, callback, 30*time.Second)
}

// QueryDBAsyncAndWaitWithTimeout 异步查询数据库并等待完成（自定义超时）
func QueryDBAsyncAndWaitWithTimeout(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error), timeout time.Duration) (interface{}, error) {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.AsyncQueryAndWait(operation, fn, callback, timeout)
}

// CheckDBHealth 检查数据库健康状态
func CheckDBHealth() error {
	wrapper := GetDBWrapper()
	return wrapper.HealthCheck()
}

// DBStats 数据库统计信息结构
type DBStats struct {
	ConnectionStats map[string]interface{} `json:"connection_stats"`
	QueryStats      map[string]interface{} `json:"query_stats"`
	HealthStats     map[string]interface{} `json:"health_stats"`
	PerformanceStats map[string]interface{} `json:"performance_stats"`
}

// GetDBStats 获取完整的数据库统计信息
func GetDBStats() *DBStats {
	stats := GetGlobalDBPoolStatsCompatible()

	return &DBStats{
		ConnectionStats: map[string]interface{}{
			"max_open_conns": stats["max_open_conns"],
			"max_idle_conns": stats["max_idle_conns"],
			"open_conns":     stats["open_conns"],
			"in_use_conns":   stats["in_use_conns"],
			"idle_conns":     stats["idle_conns"],
			"connection_utilization": stats["connection_utilization"],
		},
		QueryStats: map[string]interface{}{
			"total_queries":   stats["total_queries"],
			"success_queries": stats["success_queries"],
			"failed_queries":  stats["failed_queries"],
			"slow_queries":    stats["slow_queries"],
			"success_rate":    stats["success_rate"],
			"query_timeouts":  stats["query_timeouts"],
			"retry_attempts":  stats["retry_attempts"],
		},
		HealthStats: map[string]interface{}{
			"health_check_count":    stats["health_check_count"],
			"health_check_failures": stats["health_check_failures"],
			"last_health_check":     stats["last_health_check"],
			"health_status":         stats["health_status"],
		},
		PerformanceStats: map[string]interface{}{
			"avg_query_time": stats["avg_query_time"],
			"max_query_time": stats["max_query_time"],
			"min_query_time": stats["min_query_time"],
			"start_time":     stats["start_time"],
		},
	}
}

/*
🚀 数据库操作函数使用指南

📋 函数选择指南：

1. 需要立即获取自动生成ID（如 task.ID）：
   ✅ ExecuteDBOperation (同步)
   ✅ ExecuteDBOperationAsyncAndWait (异步+等待)
   ❌ ExecuteDBOperationAsync (纯异步，不等待)

2. 不需要立即结果的后台任务：
   ✅ ExecuteDBOperationAsync (纯异步)
   ✅ ExecuteDBOperationAsyncWithCallback (异步+回调)

3. 事务操作：
   - 需要立即结果：ExecuteDBTransaction 或 ExecuteDBTransactionAsyncAndWait
   - 后台事务：ExecuteDBTransactionAsync

4. 查询操作：
   - 需要立即结果：QueryDBAsyncAndWait
   - 后台查询：QueryDBAsync

⚠️  常见错误场景：
- 使用 ExecuteDBOperationAsync 创建任务后立即返回 task.ID
- 在需要事务一致性的场景使用纯异步函数
- 在高并发场景不设置合适的超时时间

✅ 最佳实践：
1. 创建任务记录时使用同步或异步+等待版本
2. 日志记录、统计更新使用纯异步版本
3. 设置合适的超时时间（默认30秒）
4. 在关键路径上使用同步版本确保数据一致性
5. 使用回调版本处理异步结果通知

🔧 性能考虑：
- 同步版本：阻塞当前goroutine，但确保操作完成
- 异步+等待版本：使用工作池但等待完成，平衡性能和一致性
- 纯异步版本：最高性能，但无法保证立即一致性

📊 超时设置建议：
- 简单CRUD操作：5-10秒
- 复杂查询：15-30秒
- 批量操作：30-60秒
- 事务操作：根据业务复杂度调整
*/