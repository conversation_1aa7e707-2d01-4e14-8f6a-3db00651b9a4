package test

import (
	"fmt"
	"os"
	"testing"
	"time"
	"server/core/initializer"
	"server/global"

	"github.com/stretchr/testify/assert"
)

// TestInitializationPerformance 测试初始化性能
func TestInitializationPerformance(t *testing.T) {
	// 设置测试环境
	setupTestEnvironment(t)
	defer cleanupTestEnvironment()

	// 记录开始时间
	startTime := time.Now()

	// 执行系统初始化
	initializer.InitSystem()

	// 计算总耗时
	totalDuration := time.Since(startTime)

	// 验证初始化结果
	assert.NotNil(t, global.VP, "Viper应该已初始化")
	assert.NotNil(t, global.LOG, "日志系统应该已初始化")
	assert.NotNil(t, global.DB, "数据库应该已初始化")

	// 性能断言 - 初始化应该在合理时间内完成
	assert.Less(t, totalDuration, 10*time.Second, "系统初始化应该在10秒内完成")

	t.Logf("✅ 系统初始化完成，总耗时: %v", totalDuration)
}

// BenchmarkInitialization 基准测试初始化性能
func BenchmarkInitialization(b *testing.B) {
	setupTestEnvironment(nil)
	defer cleanupTestEnvironment()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 注意：这里只能测试部分初始化逻辑，因为全量初始化会有副作用
		testPartialInitialization()
	}
}

// testPartialInitialization 测试部分初始化逻辑
func testPartialInitialization() {
	// 这里可以测试一些无副作用的初始化逻辑
	// 例如配置计算、参数验证等
}

// setupTestEnvironment 设置测试环境
func setupTestEnvironment(t *testing.T) {
	// 创建临时配置文件
	configContent := `
server:
  port: 8080
  db_type: "sqlite"
  router_prefix: "/api"
  upload_dir: "./test_upload"
  download_dir: "./test_download"
  client_bin_dir: "./test_clientbin"

sqlite:
  path: "./test.db"
  max_idle_conns: 10
  max_open_conns: 25
  prefix: ""
  singular: false

zap:
  level: "info"
  director: "./test_logs"
  show_line: true
  log_in_console: true
  retention_day: 7

jwt:
  signing_key: "test_key"
  expires_time: "7d"
  buffer_time: "1d"

admin:
  username: "admin"
  password: "admin"
`

	// 写入配置文件
	err := os.WriteFile("config.yaml", []byte(configContent), 0644)
	if err != nil && t != nil {
		t.Fatalf("创建测试配置文件失败: %v", err)
	}

	// 创建必要的目录
	dirs := []string{"./test_upload", "./test_download", "./test_clientbin", "./test_logs"}
	for _, dir := range dirs {
		os.MkdirAll(dir, 0755)
	}
}

// cleanupTestEnvironment 清理测试环境
func cleanupTestEnvironment() {
	// 清理测试文件和目录
	files := []string{"config.yaml", "test.db"}
	for _, file := range files {
		os.Remove(file)
	}

	dirs := []string{"./test_upload", "./test_download", "./test_clientbin", "./test_logs"}
	for _, dir := range dirs {
		os.RemoveAll(dir)
	}
}

// TestParallelInitialization 测试并行初始化的正确性
func TestParallelInitialization(t *testing.T) {
	setupTestEnvironment(t)
	defer cleanupTestEnvironment()

	// 多次执行初始化，确保并行逻辑的正确性
	for i := 0; i < 3; i++ {
		t.Run(fmt.Sprintf("Run_%d", i), func(t *testing.T) {
			startTime := time.Now()
			
			// 重置全局变量
			resetGlobalVariables()
			
			// 执行初始化
			initializer.InitSystem()
			
			duration := time.Since(startTime)
			t.Logf("第%d次初始化耗时: %v", i+1, duration)
			
			// 验证初始化结果
			assert.NotNil(t, global.VP)
			assert.NotNil(t, global.LOG)
			assert.NotNil(t, global.DB)
		})
	}
}

// resetGlobalVariables 重置全局变量（仅用于测试）
func resetGlobalVariables() {
	global.VP = nil
	global.LOG = nil
	global.DB = nil
	// 注意：在实际生产环境中不应该重置这些变量
}

// TestInitializationStages 测试初始化各阶段的性能
func TestInitializationStages(t *testing.T) {
	setupTestEnvironment(t)
	defer cleanupTestEnvironment()

	// 测试完整初始化的各个阶段
	t.Run("完整初始化", func(t *testing.T) {
		start := time.Now()
		initializer.InitSystem()
		duration := time.Since(start)
		t.Logf("完整初始化耗时: %v", duration)

		// 验证各组件都已正确初始化
		assert.NotNil(t, global.VP, "Viper应该已初始化")
		assert.NotNil(t, global.LOG, "日志系统应该已初始化")
		assert.NotNil(t, global.DB, "数据库应该已初始化")
	})
}
