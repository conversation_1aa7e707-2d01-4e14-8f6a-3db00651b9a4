package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"server/core/manager/workerpool"
	"server/global"
	"sync"

	"go.uber.org/zap"
)

// Serializer 通用序列化器
type Serializer struct{}

var SerializerManager = Serializer{}

// 大数据阈值 - 超过此大小的数据将使用处理池进行序列化
const LARGE_DATA_THRESHOLD = 1024 * 1024 // 1MB

// Serialize 序列化数据 - 🚀 优化：大数据使用处理池
func (s *Serializer) Serialize(data interface{}) ([]byte, error) {
	// 快速估算数据大小
	if s.isLargeData(data) {
		return s.serializeWithProcessingPool(data)
	}

	// 小数据直接序列化
	result, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("json序列化失败: %w", err)
	}
	return result, nil
}

// SerializeAsync 异步序列化 - 🚀 新增：异步序列化接口
func (s *Serializer) SerializeAsync(data interface{}, callback func([]byte, error)) error {
	return workerpool.SubmitSerializationTask("async_serialize", func() error {
		result, err := s.Serialize(data)
		callback(result, err)
		return err
	})
}

// Deserialize 反序列化数据 - 🚀 优化：大数据使用处理池
func (s *Serializer) Deserialize(data []byte, target interface{}) error {
	// 大数据使用处理池
	if len(data) > LARGE_DATA_THRESHOLD {
		return s.deserializeWithProcessingPool(data, target)
	}

	// 小数据直接反序列化
	if err := json.Unmarshal(data, target); err != nil {
		return fmt.Errorf("json反序列化失败: %w", err)
	}
	return nil
}

// DeserializeAsync 异步反序列化 - 🚀 新增：异步反序列化接口
func (s *Serializer) DeserializeAsync(data []byte, target interface{}, callback func(error)) error {
	return workerpool.SubmitSerializationTask("async_deserialize", func() error {
		err := s.Deserialize(data, target)
		callback(err)
		return err
	})
}

// serializeWithProcessingPool 使用处理池进行序列化
func (s *Serializer) serializeWithProcessingPool(data interface{}) ([]byte, error) {
	var result []byte
	var serializeErr error
	var wg sync.WaitGroup

	wg.Add(1)
	err := workerpool.SubmitSerializationTask("large_data_serialize", func() error {
		defer wg.Done()
		result, serializeErr = json.Marshal(data)
		if serializeErr != nil {
			global.LOG.Debug("处理池序列化失败", zap.Error(serializeErr))
		}
		return serializeErr
	})

	if err != nil {
		// 回退到直接序列化
		global.LOG.Warn("提交序列化任务失败，回退到直接处理", zap.Error(err))
		return json.Marshal(data)
	}

	wg.Wait()
	if serializeErr != nil {
		return nil, fmt.Errorf("json序列化失败: %w", serializeErr)
	}
	return result, nil
}

// deserializeWithProcessingPool 使用处理池进行反序列化
func (s *Serializer) deserializeWithProcessingPool(data []byte, target interface{}) error {
	var deserializeErr error
	var wg sync.WaitGroup

	wg.Add(1)
	err := workerpool.SubmitSerializationTask("large_data_deserialize", func() error {
		defer wg.Done()
		deserializeErr = json.Unmarshal(data, target)
		if deserializeErr != nil {
			global.LOG.Debug("处理池反序列化失败", zap.Error(deserializeErr))
		}
		return deserializeErr
	})

	if err != nil {
		// 回退到直接反序列化
		global.LOG.Warn("提交反序列化任务失败，回退到直接处理", zap.Error(err))
		return json.Unmarshal(data, target)
	}

	wg.Wait()
	if deserializeErr != nil {
		return fmt.Errorf("json反序列化失败: %w", deserializeErr)
	}
	return nil
}

// isLargeData 快速估算数据是否为大数据
func (s *Serializer) isLargeData(data interface{}) bool {
	if data == nil {
		return false
	}

	v := reflect.ValueOf(data)
	switch v.Kind() {
	case reflect.Slice, reflect.Array:
		// 数组/切片长度超过阈值
		return v.Len() > 1000
	case reflect.Map:
		// Map元素超过阈值
		return v.Len() > 500
	case reflect.String:
		// 字符串长度超过阈值
		return v.Len() > LARGE_DATA_THRESHOLD
	case reflect.Struct:
		// 结构体字段数量较多时认为是大数据
		return v.NumField() > 50
	default:
		return false
	}
}